"use client";

import React from 'react';
import { Button } from '@/components/ui/button';
import { DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogFooter, DialogClose } from '@/components/ui/dialog';
import { Settings2 } from 'lucide-react';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

// This component now renders only the DialogContent part.
// The <Dialog> and <DialogTrigger> are handled by the caller.
export function McpDialog() {
  // In a real app, these would come from context or state
  const [model, setModel] = React.useState("llama3");
  const [temperature, setTemperature] = React.useState(0.7);

  return (
    <DialogContent className="sm:max-w-[425px]">
      <DialogHeader>
        <DialogTitle className="flex items-center"><Settings2 size={20} className="mr-2 text-primary"/>模型控制面板 (MCP)</DialogTitle>
        <DialogDescription>
          配置 Ollama 模型设置。这些设置仅为示例。
        </DialogDescription>
      </DialogHeader>
      <div className="grid gap-4 py-4">
        <div className="grid grid-cols-4 items-center gap-4">
          <Label htmlFor="model" className="text-right">
            模型
          </Label>
          <Select value={model} onValueChange={setModel}>
            <SelectTrigger className="col-span-3">
              <SelectValue placeholder="选择一个模型" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="llama3">Llama 3</SelectItem>
              <SelectItem value="codellama">CodeLlama</SelectItem>
              <SelectItem value="mistral">Mistral</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <div className="grid grid-cols-4 items-center gap-4">
          <Label htmlFor="temperature" className="text-right">
            温度
          </Label>
          <Input
            id="temperature"
            type="number"
            value={temperature}
            onChange={(e) => setTemperature(parseFloat(e.target.value))}
            className="col-span-3"
            step="0.1"
            min="0"
            max="2"
          />
        </div>
        <p className="text-xs text-muted-foreground col-span-4 px-2">
          注意：这些控件仅用于演示，当前不影响 Ollama 行为。
        </p>
      </div>
      <DialogFooter>
        <DialogClose asChild>
          <Button type="button" variant="outline">关闭</Button>
        </DialogClose>
        <Button type="submit" disabled>保存更改</Button> 
      </DialogFooter>
    </DialogContent>
  );
}
