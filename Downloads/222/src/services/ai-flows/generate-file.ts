'use server';

import { getOllamaResponse } from '@/services/ollama';

export interface GenerateFileInput {
  prompt: string;
  currentFileName?: string;
  currentFileContent?: string;
}

export interface GenerateFileOutput {
  fileName: string;
  fileContent: string;
  explanation?: string;
}

const SYSTEM_PROMPT = `You are an expert code generation assistant.
Your task is to generate the content for a new file or modify an existing one, and suggest an appropriate filename with an extension.

You MUST respond with a valid JSON object in the following format:
{
  "fileName": "suggested_filename_with_extension",
  "fileContent": "the_generated_code_content",
  "explanation": "brief_explanation_of_what_was_generated"
}

Guidelines:
- For React components in a Next.js project, use .tsx extension
- For TypeScript files, use .ts extension
- For CSS files, use .css extension
- For configuration files, use appropriate extensions (.json, .yaml, etc.)
- The explanation should be concise and informative
- Ensure the code follows modern best practices
- Use TypeScript when appropriate
- Follow the project's existing patterns and conventions

Project Context: This is a Next.js project with <PERSON>act, TypeScript, ShadCN UI, and Tailwind CSS.`;

export async function generateFile(input: GenerateFileInput): Promise<GenerateFileOutput> {
  let prompt = `User's request: "${input.prompt}"`;
  
  if (input.currentFileName && input.currentFileContent) {
    prompt += `\n\nModifying existing file: ${input.currentFileName}\nCurrent content:\n${input.currentFileContent}`;
  } else if (input.currentFileName) {
    prompt += `\n\nSuggested filename: ${input.currentFileName}`;
  }

  prompt += `\n\nGenerate the file content, suggest an appropriate filename, and provide a brief explanation.
Return ONLY a valid JSON object with fileName, fileContent, and explanation fields.`;

  try {
    const response = await getOllamaResponse({
      prompt,
      systemPrompt: SYSTEM_PROMPT,
    });

    // Try to parse the JSON response
    let parsedResponse: GenerateFileOutput;
    try {
      // Clean the response to extract JSON
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('No JSON found in response');
      }
      
      parsedResponse = JSON.parse(jsonMatch[0]);
    } catch (parseError) {
      console.error('Failed to parse AI response as JSON:', parseError);
      console.error('Raw response:', response);
      
      // Fallback: create a response based on the raw text
      parsedResponse = {
        fileName: input.currentFileName || 'generated-file.txt',
        fileContent: response,
        explanation: 'AI response could not be parsed as JSON, returning raw content.'
      };
    }

    // Ensure output filename matches input currentFileName if provided (modification mode)
    if (input.currentFileName && parsedResponse.fileName !== input.currentFileName) {
      console.warn(`AI suggested a new filename '${parsedResponse.fileName}' during modification of '${input.currentFileName}'. Overriding with original filename.`);
      parsedResponse.fileName = input.currentFileName;
    }

    return parsedResponse;
  } catch (error) {
    console.error('Error generating file:', error);
    throw new Error(`Failed to generate file: ${(error as Error).message}`);
  }
}
