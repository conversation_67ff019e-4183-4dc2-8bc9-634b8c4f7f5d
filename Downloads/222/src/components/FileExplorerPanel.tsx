
"use client";

import React, { useState, ChangeEvent, useRef, useEffect } from 'react';
import { useAppContext } from '@/contexts/AppContext';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger, DialogFooter, DialogClose } from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { ScrollArea } from '@/components/ui/scroll-area';
import { PlusCircle, FolderPlus, UploadCloud, Trash2, Edit3, FileText, Folder, ChevronDown, ChevronRight, Settings2, Puzzle, Save, DownloadCloud, MessageSquareQuote, Wand2, FilePlus2, FolderInput, Terminal, GitMerge, BookOpen, History, AlertTriangle, ExternalLink, Copy, Scissors, ClipboardCopy, Milestone, Waypoints, Briefcase, MousePointerSquareDashed, Globe2 } from 'lucide-react';
import { Too<PERSON><PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import type { CodeFile } from '@/types';
import { PluginManagerDialog } from '@/components/dialogs/PluginManagerDialog';
import { McpDialog } from '@/components/dialogs/McpDialog';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuTrigger, DropdownMenuSub, DropdownMenuSubContent, DropdownMenuSubTrigger, DropdownMenuPortal } from '@/components/ui/dropdown-menu';

interface FileItemProps {
  file: CodeFile;
  level: number;
  onSelect: (id: string) => void;
  isActive: boolean;
  allFiles: CodeFile[];
}

const FileTreeItem: React.FC<FileItemProps> = ({ file, level, onSelect, isActive, allFiles }) => {
  const { deleteFile, updateFileName, runExplainCodeFlow, runImproveCodeFlow, toast: appContextToast, createFile: appContextCreateFile, selectFile: appContextSelectFile, getFilePath } = useAppContext();
  const [isRenaming, setIsRenaming] = useState(false);
  const [renameValue, setRenameValue] = useState(file.name);
  const [isExpanded, setIsExpanded] = useState(file.type === 'folder' ? true : false); // Default to expanded for folders
  const [isContextMenuOpen, setContextMenuOpen] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);

  const children = allFiles.filter(f => f.parentId === file.id);

  const handleRenameSubmit = () => {
    if (renameValue.trim() !== '' && renameValue.trim() !== file.name) {
      updateFileName(file.id, renameValue.trim());
    }
    setIsRenaming(false);
  };

  const handleRenameKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') handleRenameSubmit();
    if (e.key === 'Escape') {
      setRenameValue(file.name);
      setIsRenaming(false);
    }
  };
  
  useEffect(() => {
    if (isRenaming && inputRef.current) {
      inputRef.current.focus();
      inputRef.current.select();
    }
  }, [isRenaming]);

  const handleExplainCode = () => {
    if (file.type === 'file') {
      runExplainCodeFlow({ code: file.content, fileName: file.name });
    } else {
      appContextToast({ title: "操作不适用", description: "只能对文件解释代码。", variant: "default" });
    }
  };

  const handleImproveCode = () => {
    if (file.type === 'file') {
      runImproveCodeFlow({ code: file.content, fileName: file.name });
    } else {
      appContextToast({ title: "操作不适用", description: "只能对文件改进代码。", variant: "default" });
    }
  };

  const handleNotImplemented = (featureName: string) => {
    appContextToast({ title: "功能即将推出", description: `${featureName} 功能正在开发中。`, duration: 3000 });
  };
  
  const handleAddNewFileInContext = () => {
    const parentId = file.type === 'folder' ? file.id : file.parentId;
    const newFileName = prompt("输入新文件名:", "newFile.txt");
    if (newFileName) {
      const createdFile = appContextCreateFile(newFileName, 'file', parentId);
      if (createdFile) appContextSelectFile(createdFile.id);
    }
  };

  const handleAddNewFolderInContext = () => {
    const parentId = file.type === 'folder' ? file.id : file.parentId;
    const newFolderName = prompt("输入新文件夹名称:", "newFolder");
    if (newFolderName) {
      appContextCreateFile(newFolderName, 'folder', parentId);
    }
  };

  const handleCopyPath = async () => {
    const path = getFilePath(file.id, allFiles);
    try {
      await navigator.clipboard.writeText(path);
      appContextToast({ title: "路径已复制", description: `路径 "${path}" 已复制到剪贴板。` });
    } catch (err) {
      console.error('无法复制路径: ', err);
      appContextToast({ title: "复制失败", description: "无法将路径复制到剪贴板。", variant: "destructive" });
    }
  };

  const handleLeftClick = () => {
    if (file.type === 'file') {
      onSelect(file.id);
    } else { // Folder
      setIsExpanded(!isExpanded);
    }
    setContextMenuOpen(false); // Ensure context menu doesn't open on left click
  };

  const handleRightClick = (event: React.MouseEvent) => {
    event.preventDefault();
    setContextMenuOpen(true);
  };

  return (
    <>
      <DropdownMenu open={isContextMenuOpen} onOpenChange={setContextMenuOpen}>
        <DropdownMenuTrigger asChild>
          <div 
            className={`flex items-center justify-between py-1.5 px-2 rounded-md group cursor-pointer hover:bg-[hsl(var(--sidebar-accent))] ${isActive && file.type === 'file' ? 'bg-[hsl(var(--sidebar-primary))] text-[hsl(var(--sidebar-primary-foreground))]' : 'text-[hsl(var(--sidebar-foreground))]'}`}
            style={{ paddingLeft: `${level * 12 + (file.type === 'folder' ? 4 : 20)}px` }}
            onClick={handleLeftClick}
            onContextMenu={handleRightClick}
          >
            <div className="flex items-center overflow-hidden whitespace-nowrap text-ellipsis">
              {file.type === 'folder' && (
                isExpanded ? <ChevronDown size={16} className="mr-1 shrink-0" /> : <ChevronRight size={16} className="mr-1 shrink-0" />
              )}
              {file.type === 'file' ? <FileText size={14} className="mr-2 shrink-0" /> : <Folder size={14} className="mr-2 shrink-0" />}
              {isRenaming ? (
                <Input
                  ref={inputRef}
                  type="text"
                  value={renameValue}
                  onChange={(e) => setRenameValue(e.target.value)}
                  onBlur={handleRenameSubmit}
                  onKeyDown={handleRenameKeyDown}
                  className="h-6 px-1 text-xs bg-background text-foreground"
                  onClick={(e) => e.stopPropagation()} // Prevent left click action while renaming
                  aria-label={`重命名 ${file.name}`}
                />
              ) : (
                <span className="truncate">{file.name}</span>
              )}
            </div>
          </div>
        </DropdownMenuTrigger>
        <DropdownMenuContent className="w-60" sideOffset={5} onCloseAutoFocus={(e) => e.preventDefault()}>
          <DropdownMenuLabel>{file.name}</DropdownMenuLabel>
          <DropdownMenuSeparator />
          {file.type === 'file' && (
            <DropdownMenuItem onClick={() => onSelect(file.id)}>
              <ExternalLink className="mr-2 h-4 w-4" />
              <span>打开</span>
            </DropdownMenuItem>
          )}
           <DropdownMenuItem onClick={() => handleNotImplemented("在集成终端中打开")} disabled>
            <Terminal className="mr-2 h-4 w-4" />
            <span>在集成终端中打开</span>
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => handleNotImplemented("选择文件作为上下文")} disabled={file.type !== 'file'}>
            <FolderInput className="mr-2 h-4 w-4" />
            <span>选择文件作为上下文</span>
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => handleNotImplemented("选择以进行比较")} disabled>
            <Waypoints className="mr-2 h-4 w-4" />
            <span>选择以进行比较</span>
          </DropdownMenuItem>

          <DropdownMenuSeparator />
          
          <DropdownMenuSub>
              <DropdownMenuSubTrigger>
                  <Briefcase className="mr-2 h-4 w-4" />
                  <span>AI 操作</span>
              </DropdownMenuSubTrigger>
              <DropdownMenuPortal>
                  <DropdownMenuSubContent>
                      <DropdownMenuItem onClick={handleExplainCode} disabled={file.type !== 'file'}>
                          <MessageSquareQuote className="mr-2 h-4 w-4" />
                          <span>解释代码</span>
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={handleImproveCode} disabled={file.type !== 'file'}>
                          <Wand2 className="mr-2 h-4 w-4" />
                          <span>改进代码</span>
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => handleNotImplemented("Copilot 功能")} disabled>
                          <Puzzle className="mr-2 h-4 w-4" />
                          <span>Copilot...</span>
                      </DropdownMenuItem>
                  </DropdownMenuSubContent>
              </DropdownMenuPortal>
          </DropdownMenuSub>

          <DropdownMenuSub>
              <DropdownMenuSubTrigger>
                  <GitMerge className="mr-2 h-4 w-4" />
                  <span>Git</span>
              </DropdownMenuSubTrigger>
              <DropdownMenuPortal>
                  <DropdownMenuSubContent>
                      <DropdownMenuItem disabled onClick={() => handleNotImplemented("Git Commit")}>Commit</DropdownMenuItem>
                      <DropdownMenuItem disabled onClick={() => handleNotImplemented("Git Push")}>Push</DropdownMenuItem>
                      <DropdownMenuItem disabled onClick={() => handleNotImplemented("Git Pull")}>Pull</DropdownMenuItem>
                      <DropdownMenuItem disabled onClick={() => handleNotImplemented("Open Changes")}>Open Changes</DropdownMenuItem>
                  </DropdownMenuSubContent>
              </DropdownMenuPortal>
          </DropdownMenuSub>
          <DropdownMenuItem onClick={() => handleNotImplemented("文件历史")} disabled>
            <History className="mr-2 h-4 w-4" />
            <span>文件历史 / 打开时间线</span>
          </DropdownMenuItem>
          
          <DropdownMenuSeparator />

          <DropdownMenuItem onClick={() => handleNotImplemented("剪切")} disabled>
            <Scissors className="mr-2 h-4 w-4" />
            <span>剪切</span>
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => handleNotImplemented("复制")} disabled>
            <Copy className="mr-2 h-4 w-4" />
            <span>复制</span>
          </DropdownMenuItem>
          <DropdownMenuItem onClick={handleCopyPath}>
            <ClipboardCopy className="mr-2 h-4 w-4" />
            <span>复制路径</span>
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => handleNotImplemented("复制相对路径")} disabled>
            <Milestone className="mr-2 h-4 w-4" />
            <span>复制相对路径</span>
          </DropdownMenuItem>
          
          <DropdownMenuSeparator />
          <DropdownMenuItem onClick={handleAddNewFileInContext}>
            <FilePlus2 className="mr-2 h-4 w-4" />
            <span>新建文件</span>
          </DropdownMenuItem>
          <DropdownMenuItem onClick={handleAddNewFolderInContext}>
            <FolderPlus className="mr-2 h-4 w-4" />
            <span>新建文件夹</span>
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => { setIsRenaming(true); setContextMenuOpen(false); }}>
            <Edit3 className="mr-2 h-4 w-4" />
            <span>重命名...</span>
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => deleteFile(file.id)} className="text-destructive focus:text-destructive focus:bg-destructive/10">
            <Trash2 className="mr-2 h-4 w-4" />
            <span>删除</span>
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
      {file.type === 'folder' && isExpanded && (
        <div className="block"> {/* Ensure children are always block for layout */}
          {children.length > 0 ? renderFileTreeChildren(children, level + 1, onSelect, isActive, allFiles) : null}
        </div>
      )}
    </>
  );
};

// Helper function to render children, to avoid direct recursion issues with hooks in map
const renderFileTreeChildren = (
    children: CodeFile[], 
    level: number, 
    onSelect: (id: string) => void, 
    isActiveItem: (id: string) => boolean, // Renamed to avoid conflict
    allFiles: CodeFile[]
  ) => {
  return children
    .sort((a, b) => { 
      if (a.type === 'folder' && b.type === 'file') return -1;
      if (a.type === 'file' && b.type === 'folder') return 1;
      return a.name.localeCompare(b.name);
    })
    .map(childFile => (
      <FileTreeItem 
        key={childFile.id}
        file={childFile} 
        level={level} 
        onSelect={onSelect}
        isActive={isActiveItem(childFile.id)} // Use the passed function
        allFiles={allFiles}
      />
    ));
};


export function FileExplorerPanel() {
  const { files, createFile, selectFile, activeFile, saveActiveFile, downloadAllFilesAsZip, toast, isFileSystemAccessSupported, promptAndLoadFromDirectory, isLoadingFromDirectory, startNewInMemoryProject } = useAppContext();
  const [newFileName, setNewFileName] = useState('');
  const [newFolderName, setNewFolderName] = useState('');
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileOpen = (event: ChangeEvent<HTMLInputElement>) => {
    const fileList = event.target.files;
    if (fileList && fileList.length > 0) {
      const file = fileList[0];
      const reader = new FileReader();
      reader.onload = (e) => {
        const content = e.target?.result as string;
        // This will replace all current files if you want to "open a single file" project
        // For adding to existing, you'd need a different logic path
        // For now, let's assume it creates a new project context or adds to root.
        // To prevent data loss, this should probably prompt the user or be a "load project from single file"
        createFile(file.name, 'file', null, content); 
        toast({ title: "文件已打开", description: `"${file.name}" 已加载其内容。` });
      };
      reader.readAsText(file);
      if (event.target) event.target.value = ""; // Reset file input
    }
  };
  
  const renderFileTree = (parentId: string | null = null, level: number = 0): JSX.Element[] => {
    const currentLevelFiles = files
      .filter(file => file.parentId === parentId)
      .sort((a, b) => { 
        if (a.type === 'folder' && b.type === 'file') return -1;
        if (a.type === 'file' && b.type === 'folder') return 1;
        return a.name.localeCompare(b.name);
      });
    
    return currentLevelFiles.map(file => (
      <FileTreeItem 
        key={file.id}
        file={file} 
        level={level} 
        onSelect={selectFile}
        isActive={activeFile?.id === file.id && file.type === 'file'} // isActive only for files
        allFiles={files}
      />
    ));
  };


  return (
    <div className="h-full flex flex-col bg-[hsl(var(--sidebar-background))] text-[hsl(var(--sidebar-foreground))] border-r border-[hsl(var(--sidebar-border))]" style={{ minWidth: '200px', maxWidth: '400px' }}>
      <TooltipProvider>
        <div className="flex items-center space-x-1 p-2 border-b border-[hsl(var(--sidebar-border))]">
          <Dialog>
            <Tooltip>
              <TooltipTrigger asChild>
                <DialogTrigger asChild>
                  <Button variant="ghost" size="icon" aria-label="新建文件">
                    <PlusCircle size={16} />
                  </Button>
                </DialogTrigger>
              </TooltipTrigger>
              <TooltipContent>
                <p>新建文件 (根目录)</p>
              </TooltipContent>
            </Tooltip>
            <DialogContent>
              <DialogHeader><DialogTitle>创建新文件</DialogTitle></DialogHeader>
              <div className="grid gap-4 py-4">
                <Label htmlFor="fileNameNFEPanel">文件名</Label>
                <Input id="fileNameNFEPanel" value={newFileName} onChange={(e) => setNewFileName(e.target.value)} placeholder="例如：script.js" />
              </div>
              <DialogFooter>
                <DialogClose asChild><Button type="button" variant="outline">取消</Button></DialogClose>
                <Button onClick={() => { createFile(newFileName, 'file'); setNewFileName(''); DialogClose(); }} disabled={!newFileName.trim()}>创建</Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>

          <Dialog>
            <Tooltip>
              <TooltipTrigger asChild>
                <DialogTrigger asChild>
                  <Button variant="ghost" size="icon" aria-label="新建文件夹">
                    <FolderPlus size={16} />
                  </Button>
                </DialogTrigger>
              </TooltipTrigger>
              <TooltipContent>
                <p>新建文件夹 (根目录)</p>
              </TooltipContent>
            </Tooltip>
            <DialogContent>
              <DialogHeader><DialogTitle>创建新文件夹</DialogTitle></DialogHeader>
              <div className="grid gap-4 py-4">
                <Label htmlFor="folderNameNFEPanel">文件夹名称</Label>
                <Input id="folderNameNFEPanel" value={newFolderName} onChange={(e) => setNewFolderName(e.target.value)} placeholder="例如：components" />
              </div>
              <DialogFooter>
                <DialogClose asChild><Button type="button" variant="outline">取消</Button></DialogClose>
                <Button onClick={() => { createFile(newFolderName, 'folder'); setNewFolderName(''); DialogClose(); }} disabled={!newFolderName.trim()}>创建</Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
          
          {isFileSystemAccessSupported && (
             <Tooltip>
              <TooltipTrigger asChild>
                <Button variant="ghost" size="icon" onClick={async () => {
                    const success = await promptAndLoadFromDirectory();
                    // Logic to transition to editor view is handled in page.tsx based on success
                }} disabled={isLoadingFromDirectory} aria-label="打开本地文件夹">
                  <UploadCloud size={16} />
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>打开本地文件夹 (替换当前项目)</p>
              </TooltipContent>
            </Tooltip>
          )}

          <Tooltip>
            <TooltipTrigger asChild>
              {/* This button is problematic if it replaces the whole project. 
                  Maybe it should add to the current project or be disabled if a project is loaded.
                  For now, its behavior is to add the file to the root.
              */}
              <Button variant="ghost" size="icon" onClick={() => fileInputRef.current?.click()} aria-label="打开单个文件">
                <FileText size={16} />
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>打开单个文件 (添加到根目录)</p>
            </TooltipContent>
          </Tooltip>
          <input type="file" ref={fileInputRef} onChange={handleFileOpen} className="hidden" accept=".*" />

          <Tooltip>
            <TooltipTrigger asChild>
              <Button variant="ghost" size="icon" onClick={saveActiveFile} disabled={!activeFile && files.length === 0} aria-label="保存所有更改">
                <Save size={16} />
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>保存所有更改 (浏览器缓存)</p>
            </TooltipContent>
          </Tooltip>

          <Tooltip>
            <TooltipTrigger asChild>
              <Button variant="ghost" size="icon" onClick={downloadAllFilesAsZip} disabled={files.filter(f => f.type === 'file').length === 0} aria-label="下载所有文件 (ZIP)">
                <DownloadCloud size={16} />
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>下载所有文件 (ZIP)</p>
            </TooltipContent>
          </Tooltip>
          
          <Dialog>
            <Tooltip>
              <TooltipTrigger asChild>
                <DialogTrigger asChild>
                  <Button variant="ghost" size="icon" aria-label="插件">
                    <Puzzle size={16} />
                  </Button>
                </DialogTrigger>
              </TooltipTrigger>
              <TooltipContent>
                <p>插件 (演示)</p>
              </TooltipContent>
            </Tooltip>
            <PluginManagerDialog />
          </Dialog>

          <Dialog>
            <Tooltip>
              <TooltipTrigger asChild>
                <DialogTrigger asChild>
                  <Button variant="ghost" size="icon" aria-label="模型控制面板">
                    <Settings2 size={16} />
                  </Button>
                </DialogTrigger>
              </TooltipTrigger>
              <TooltipContent>
                <p>模型控制面板 (演示)</p>
              </TooltipContent>
            </Tooltip>
            <McpDialog />
          </Dialog>
        </div>
      </TooltipProvider>
      <ScrollArea className="flex-grow p-2">
        {files.length === 0 ? (
          <div className="text-center text-xs text-[hsl(var(--muted-foreground))] p-4 space-y-2">
            <p>项目中还没有文件。</p>
            <div className="flex flex-col sm:flex-row items-center justify-center gap-2">
                <Button size="sm" variant="outline" onClick={() => {
                    const newFile = createFile("untitled.txt", 'file');
                    if (newFile) selectFile(newFile.id);
                }}>
                开始一个新文件
                </Button>
                {isFileSystemAccessSupported && (
                    <Button size="sm" variant="outline" onClick={promptAndLoadFromDirectory} disabled={isLoadingFromDirectory}>
                        {isLoadingFromDirectory ? "加载中..." : "导入本地文件夹"}
                    </Button>
                )}
            </div>
             <Button size="sm" variant="link" className="text-xs" onClick={startNewInMemoryProject}>
                或开始一个完全空的项目
            </Button>
          </div>
        ) : (
          renderFileTree(null)
        )}
      </ScrollArea>
    </div>
  );
}

