// The explain-selected-code flow takes selected code and a prompt, and returns an explanation of the code.
'use server';

import {ai} from '@/ai/genkit';
import {z} from 'genkit';

const ExplainSelectedCodeInputSchema = z.object({
  code: z.string().describe('The code to be explained.'),
  prompt: z.string().describe('The prompt for the code explanation.'),
});
export type ExplainSelectedCodeInput = z.infer<typeof ExplainSelectedCodeInputSchema>;

const ExplainSelectedCodeOutputSchema = z.object({
  explanation: z.string().describe('The explanation of the code.'),
});
export type ExplainSelectedCodeOutput = z.infer<typeof ExplainSelectedCodeOutputSchema>;

export async function explainSelectedCode(input: ExplainSelectedCodeInput): Promise<ExplainSelectedCodeOutput> {
  return explainSelectedCodeFlow(input);
}

const prompt = ai.definePrompt({
  name: 'explainSelectedCodePrompt',
  input: {schema: ExplainSelectedCodeInputSchema},
  output: {schema: ExplainSelectedCodeOutputSchema},
  prompt: `Explain the following code based on the prompt.  The prompt is {{{prompt}}}.\n\n The code is: {{{code}}}`,
});

const explainSelectedCodeFlow = ai.defineFlow(
  {
    name: 'explainSelectedCodeFlow',
    inputSchema: ExplainSelectedCodeInputSchema,
    outputSchema: ExplainSelectedCodeOutputSchema,
  },
  async input => {
    const {output} = await prompt(input);
    return output!;
  }
);
