{"name": "ollama-code", "version": "0.1.0", "private": true, "main": "electron/main.js", "homepage": "./", "scripts": {"dev": "next dev --turbopack -p 3000", "build": "next build", "start": "next start", "lint": "next lint", "typecheck": "tsc --noEmit", "electron": "electron .", "electron:dev": "concurrently \"npm run dev\" \"wait-on http://localhost:3000 && electron .\"", "electron:pack": "npm run build && electron-builder", "electron:dist": "npm run build && electron-builder --publish=never", "postinstall": "electron-builder install-app-deps"}, "dependencies": {"@hookform/resolvers": "^4.1.3", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-menubar": "^1.1.6", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-radio-group": "^1.2.3", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slider": "^1.2.3", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-toast": "^1.2.6", "@radix-ui/react-tooltip": "^1.1.8", "@tanstack-query-firebase/react": "^1.0.5", "@tanstack/react-query": "^5.66.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^3.6.0", "dotenv": "^16.5.0", "firebase": "^11.8.1", "jszip": "^3.10.1", "lucide-react": "^0.475.0", "next": "15.2.3", "node-fetch": "^2.7.0", "patch-package": "^8.0.0", "react": "^18.3.1", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-hook-form": "^7.54.2", "react-markdown": "^9.0.1", "react-resizable-panels": "^2.0.20", "recharts": "^2.15.1", "remark-gfm": "^4.0.0", "tailwind-merge": "^3.0.1", "tailwindcss-animate": "^1.0.7", "zod": "^3.24.2"}, "devDependencies": {"@tailwindcss/typography": "^0.5.13", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "concurrently": "^9.1.2", "electron": "^36.5.0", "electron-builder": "^26.0.12", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5", "wait-on": "^8.0.3"}, "build": {"appId": "com.ollamacode.app", "productName": "OllamaCode", "directories": {"output": "dist"}, "files": ["out/**/*", "electron/**/*", "node_modules/**/*", "package.json"], "mac": {"category": "public.app-category.developer-tools", "target": [{"target": "dmg", "arch": ["x64", "arm64"]}]}, "win": {"target": [{"target": "nsis", "arch": ["x64"]}]}, "linux": {"target": [{"target": "AppImage", "arch": ["x64"]}]}}}