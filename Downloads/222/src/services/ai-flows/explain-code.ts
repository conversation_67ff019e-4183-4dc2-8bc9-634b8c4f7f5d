'use server';

import { getOllamaResponse } from '@/services/ollama';

export interface ExplainCodeInput {
  code: string;
  prompt?: string;
  fileName?: string;
}

export interface ExplainCodeOutput {
  explanation: string;
}

const SYSTEM_PROMPT = `You are an expert code analysis assistant.
Your task is to explain code in a clear, comprehensive, and educational manner.

Guidelines:
- Provide clear explanations that are easy to understand
- Break down complex concepts into simpler parts
- Explain the purpose and functionality of the code
- Mention any design patterns, best practices, or potential issues
- Use markdown formatting for better readability
- Be concise but thorough
- Adapt your explanation style based on any specific instructions in the user's prompt

You MUST respond with a valid JSON object in the following format:
{
  "explanation": "your_detailed_explanation_in_markdown"
}`;

export async function explainCode(input: ExplainCodeInput): Promise<ExplainCodeOutput> {
  let prompt = `Please explain the following code:`;
  
  if (input.fileName) {
    prompt += `\n\nFile: ${input.fileName}`;
  }
  
  prompt += `\n\nCode:\n\`\`\`\n${input.code}\n\`\`\``;
  
  if (input.prompt) {
    prompt += `\n\nSpecific instructions: ${input.prompt}`;
  }

  prompt += `\n\nProvide a detailed explanation of this code.
Return ONLY a valid JSON object with an explanation field containing markdown-formatted text.`;

  try {
    const response = await getOllamaResponse({
      prompt,
      systemPrompt: SYSTEM_PROMPT,
    });

    // Try to parse the JSON response
    let parsedResponse: ExplainCodeOutput;
    try {
      // Clean the response to extract JSON
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('No JSON found in response');
      }
      
      parsedResponse = JSON.parse(jsonMatch[0]);
    } catch (parseError) {
      console.error('Failed to parse AI response as JSON:', parseError);
      console.error('Raw response:', response);
      
      // Fallback: create a response based on the raw text
      parsedResponse = {
        explanation: response
      };
    }

    return parsedResponse;
  } catch (error) {
    console.error('Error explaining code:', error);
    throw new Error(`Failed to explain code: ${(error as Error).message}`);
  }
}
