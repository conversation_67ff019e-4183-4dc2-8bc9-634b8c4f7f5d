# OllamaCode - 本地AI代码编辑器

一个集成了Ollama AI的本地桌面代码编辑器，支持AI辅助编程功能。

## ✨ 功能特性

- 🖥️ **桌面应用**: 基于Electron的本地桌面应用
- 🤖 **AI集成**: 使用Ollama提供本地AI功能
- 📝 **代码编辑**: 完整的代码编辑器功能
- 🔧 **AI辅助**:
  - 代码生成
  - 代码解释
  - 代码改进建议
- 🎨 **现代界面**: 基于React + Next.js + ShadCN UI
- 🌙 **深色主题**: 护眼的深色界面

## 🚀 快速开始

### 前置要求

1. **Node.js** (版本 18+)
2. **Ollama** 已安装并运行
   ```bash
   # 安装 Ollama (macOS)
   brew install ollama

   # 启动 Ollama 服务
   ollama serve

   # 下载模型 (推荐)
   ollama pull llama3.2
   ```

### 安装和运行

1. **安装依赖**
   ```bash
   npm install
   ```

2. **启动应用**
   ```bash
   # 使用启动脚本 (推荐)
   ./start-app.sh

   # 或者直接运行开发模式
   npm run electron:dev
   ```

## 📋 可用脚本

- `npm run dev` - 启动Next.js开发服务器
- `npm run electron:dev` - 启动Electron开发模式
- `npm run electron` - 运行Electron应用
- `npm run build` - 构建Next.js应用
- `npm run electron:pack` - 打包桌面应用
- `npm run typecheck` - TypeScript类型检查

## 🔧 配置

### Ollama配置

应用会自动连接到本地Ollama服务 (`http://localhost:11434`)。

支持的环境变量：
- `OLLAMA_BASE_URL` - Ollama API地址 (默认: http://localhost:11434/api/generate)
- `OLLAMA_MODEL` - 默认模型 (默认: llama3.2:latest)

### 模型选择

应用支持所有已安装的Ollama模型。在界面中可以选择不同的模型进行AI操作。

## 🎯 使用方法

1. **启动应用**: 运行 `./start-app.sh` 或 `npm run electron:dev`
2. **创建项目**: 点击"新建项目"或加载现有文件夹
3. **编写代码**: 在编辑器中编写代码
4. **AI辅助**:
   - 选择代码后点击"解释代码"
   - 使用提示面板生成新代码
   - 获取代码改进建议

## 🛠️ 技术栈

- **前端**: React 18 + Next.js 15
- **桌面**: Electron
- **UI组件**: ShadCN UI + Tailwind CSS
- **AI集成**: Ollama API
- **语言**: TypeScript

## 📦 项目结构

```
src/
├── app/                 # Next.js App Router
├── components/          # React组件
├── contexts/           # React Context
├── services/           # 服务层
│   ├── ollama.ts       # Ollama API集成
│   └── ai-flows/       # AI功能流程
├── types/              # TypeScript类型定义
└── lib/                # 工具函数

electron/
├── main.js             # Electron主进程
└── preload.js          # 预加载脚本
```

## 🔍 故障排除

### Ollama连接问题
- 确保Ollama服务正在运行: `ollama serve`
- 检查端口11434是否可访问
- 确认已下载至少一个模型: `ollama list`

### 应用启动问题
- 检查Node.js版本 (需要18+)
- 清除node_modules并重新安装: `rm -rf node_modules && npm install`
- 检查TypeScript错误: `npm run typecheck`

## 📄 许可证

MIT License

## 🤝 贡献

欢迎提交Issue和Pull Request！

---

**享受本地AI编程的乐趣！** 🎉
