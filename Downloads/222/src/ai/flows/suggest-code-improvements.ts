'use server';

/**
 * @fileOverview A code improvement suggestion AI agent.
 *
 * - suggestCodeImprovements - A function that handles the code improvement process.
 * - SuggestCodeImprovementsInput - The input type for the suggestCodeImprovements function.
 * - SuggestCodeImprovementsOutput - The return type for the suggestCodeImprovements function.
 */

import {ai} from '@/ai/genkit';
import {z} from 'genkit';

const SuggestCodeImprovementsInputSchema = z.object({
  code: z.string().describe('The code to be improved.'),
  programmingLanguage: z.string().describe('The programming language of the code.'),
});
export type SuggestCodeImprovementsInput = z.infer<
  typeof SuggestCodeImprovementsInputSchema
>;

const SuggestCodeImprovementsOutputSchema = z.object({
  improvedCode: z
    .string()
    .describe('The improved code with bug fixes, performance optimizations, or security enhancements.'),
  explanation: z
    .string()
    .describe('An explanation of the improvements made to the code.'),
});
export type SuggestCodeImprovementsOutput = z.infer<
  typeof SuggestCodeImprovementsOutputSchema
>;

export async function suggestCodeImprovements(
  input: SuggestCodeImprovementsInput
): Promise<SuggestCodeImprovementsOutput> {
  return suggestCodeImprovementsFlow(input);
}

const prompt = ai.definePrompt({
  name: 'suggestCodeImprovementsPrompt',
  input: {schema: SuggestCodeImprovementsInputSchema},
  output: {schema: SuggestCodeImprovementsOutputSchema},
  prompt: `You are an expert code reviewer.

You will be provided with a block of code and your task is to suggest improvements to it.
These improvements might include bug fixes, performance optimizations, or security enhancements.

You should return the improved code and an explanation of the improvements made.

Programming Language: {{{programmingLanguage}}}
Code: {{{code}}}`,
});

const suggestCodeImprovementsFlow = ai.defineFlow(
  {
    name: 'suggestCodeImprovementsFlow',
    inputSchema: SuggestCodeImprovementsInputSchema,
    outputSchema: SuggestCodeImprovementsOutputSchema,
  },
  async input => {
    const {output} = await prompt(input);
    return output!;
  }
);
