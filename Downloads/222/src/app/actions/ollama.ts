
'use server';

import type { OllamaStreamChunk } from '@/types';

interface OllamaRequestPayload {
  model: string;
  prompt: string;
  stream: boolean;
  images?: string[]; // For multimodal models like LLaVA
  options?: Record<string, any>;
}

interface OllamaTagsResponse {
  models: Array<{
    name: string;
    model: string;
    modified_at: string;
    size: number;
    digest: string;
    details: {
      parent_model: string;
      format: string;
      family: string;
      families: string[] | null;
      parameter_size: string;
      quantization_level: string;
    };
  }>;
}

const OLLAMA_API_URL = process.env.OLLAMA_BASE_URL || 'http://localhost:11434/api/generate';
const OLLAMA_CHAT_API_URL = process.env.OLLAMA_CHAT_BASE_URL || 'http://localhost:11434/api/chat';
const OLLAMA_TAGS_API_URL = process.env.OLLAMA_TAGS_BASE_URL || 'http://localhost:11434/api/tags';

const DEFAULT_MODEL = process.env.OLLAMA_MODEL || "llama3";

export async function getLocalOllamaModels(): Promise<string[]> {
  try {
    const response = await fetch(OLLAMA_TAGS_API_URL);
    if (!response.ok) {
      console.error(`Ollama API Error fetching tags (${response.status}): ${await response.text()}`);
      return [];
    }
    const data: OllamaTagsResponse = await response.json();
    return data.models.map(model => model.name).sort();
  } catch (error) {
    console.error('Failed to connect to Ollama to fetch tags:', error);
    return [];
  }
}

export async function streamOllamaResponse({
  prompt,
  model = DEFAULT_MODEL,
  useChatEndpoint = false,
  fileDataUri = null
}: {
  prompt: string;
  model?: string,
  useChatEndpoint?: boolean,
  fileDataUri?: string | null
}): Promise<ReadableStream<OllamaStreamChunk> | null> {
  const apiUrl = useChatEndpoint ? OLLAMA_CHAT_API_URL : OLLAMA_API_URL;

  const payload: OllamaRequestPayload = {
    model: model,
    prompt: prompt,
    stream: true,
  };

  if (useChatEndpoint) {
    // @ts-ignore
    payload.messages = [{ role: "user", content: prompt }];
    // @ts-ignore
    delete payload.prompt;
  } else {
    if (fileDataUri && fileDataUri.startsWith('data:image')) {
      try {
        const base64Image = fileDataUri.split(',')[1];
        if (base64Image) {
          payload.images = [base64Image];
        }
      } catch (e) {
        console.error("Error processing image data URI for Ollama:", e);
      }
    }
  }


  try {
    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(payload),
    });

    if (!response.ok) {
      const errorBody = await response.text();
      console.error(`Ollama API Error (${response.status}): ${errorBody}`);
      return new ReadableStream({
        start(controller) {
          controller.enqueue({ model, created_at: new Date().toISOString(), error: `Ollama API Error (${response.status}): ${errorBody}`, done: true });
          controller.close();
        }
      });
    }

    if (!response.body) {
       return new ReadableStream({
        start(controller) {
          controller.enqueue({ model, created_at: new Date().toISOString(), error: 'Ollama response body is null.', done: true });
          controller.close();
        }
      });
    }

    const reader = response.body.getReader();
    const decoder = new TextDecoder();

    return new ReadableStream<OllamaStreamChunk>({
      async start(controller) {
        try {
          while (true) {
            const { done, value } = await reader.read();
            if (done) break;

            const chunkString = decoder.decode(value, { stream: true });
            const jsonObjects = chunkString.split('\n').filter(str => str.trim() !== '');

            for (const jsonObjStr of jsonObjects) {
              try {
                const parsedChunk = JSON.parse(jsonObjStr) as OllamaStreamChunk;
                controller.enqueue(parsedChunk);
                if (parsedChunk.done && parsedChunk.error) {
                   console.error("Ollama stream returned an error:", parsedChunk.error);
                }
              } catch (e) {
                console.error('Failed to parse Ollama stream chunk:', jsonObjStr, e);
                controller.enqueue({ model, created_at: new Date().toISOString(), error: `Failed to parse stream chunk: ${(e as Error).message}`, done: true });
              }
            }
          }
        } catch (error) {
          console.error('Error reading from Ollama stream:', error);
          controller.enqueue({ model, created_at: new Date().toISOString(), error: `Stream read error: ${(error as Error).message}`, done: true });
        } finally {
          controller.close();
          reader.releaseLock();
        }
      },
      cancel() {
        reader.cancel();
        console.log("Ollama stream cancelled");
      }
    });

  } catch (error) {
    console.error('Failed to connect to Ollama:', error);
     return new ReadableStream({
        start(controller) {
          controller.enqueue({ model, created_at: new Date().toISOString(), error: `Connection to Ollama failed: ${(error as Error).message}`, done: true });
          controller.close();
        }
      });
  }
}
