
'use server';
/**
 * @fileOverview A Genkit flow to suggest improvements for selected code.
 *
 * - improveCode - A function that handles code improvement suggestions.
 * - ImproveCodeInput - The input type for the improveCode function.
 * - ImproveCodeOutput - The return type for the improveCode function.
 */

import {ai} from '@/ai/genkit';
import {z} from 'genkit';

const ImproveCodeInputSchema = z.object({
  code: z.string().describe('The code to be improved.'),
  prompt: z.string().optional().describe('An optional prompt to guide the improvement (e.g., "refactor for readability", "optimize for performance", "fix potential bugs").'),
  fileName: z.string().optional().describe('The name of the file containing the code, for context.'),
  programmingLanguage: z.string().optional().describe('The programming language of the code, if known.'),
});
export type ImproveCodeInput = z.infer<typeof ImproveCodeInputSchema>;

const ImproveCodeOutputSchema = z.object({
  improvedCode: z.string().optional().describe('The suggested improved code. If only an explanation is relevant, this might be omitted.'),
  explanation: z.string().describe('An explanation of the suggested improvements, or why no changes are needed. Formatted in Markdown.'),
});
export type ImproveCodeOutput = z.infer<typeof ImproveCodeOutputSchema>;

export async function improveCode(input: ImproveCodeInput): Promise<ImproveCodeOutput> {
  return improveCodeFlow(input);
}

const promptTemplate = ai.definePrompt({
  name: 'improveCodePrompt',
  input: {schema: ImproveCodeInputSchema},
  output: {schema: ImproveCodeOutputSchema},
  prompt: `You are an expert code reviewer and senior software engineer.
Your task is to analyze the provided code and suggest improvements or explain how it could be enhanced.

{{#if fileName}}
The code is from the file: "{{fileName}}".
{{/if}}
{{#if programmingLanguage}}
The programming language is: {{programmingLanguage}}.
{{/if}}

The code to review is:
\`\`\`{{#if programmingLanguage}}{{programmingLanguage}}{{/if}}
{{{code}}}
\`\`\`

{{#if prompt}}
User's specific request for improvement: "{{prompt}}"
Focus on this request primarily.
{{else}}
Analyze the code for potential bugs, performance bottlenecks, security vulnerabilities, and areas where readability or maintainability could be improved.
{{/if}}

If you suggest code changes, provide the improved code block.
Always provide a clear explanation of your suggestions or observations in Markdown.
If the code is already good and no specific improvements are obvious given the context, state that and briefly explain why.

Respond with the 'improvedCode' (if applicable) and 'explanation' fields. The explanation should be in Markdown.
`,
});

const improveCodeFlow = ai.defineFlow(
  {
    name: 'improveCodeFlow',
    inputSchema: ImproveCodeInputSchema,
    outputSchema: ImproveCodeOutputSchema,
  },
  async (input) => {
    const {output} = await promptTemplate(input);
    if (!output) {
      throw new Error("AI failed to generate improvement suggestions for the code.");
    }
    return output;
  }
);
