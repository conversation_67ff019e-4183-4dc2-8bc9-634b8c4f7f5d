// The use server directive is necessary here because we're importing genkit, which uses server-only code.
'use server';

/**
 * @fileOverview A code generation AI agent.
 *
 * - generateCodeFromPrompt - A function that generates code from a prompt.
 * - GenerateCodeFromPromptInput - The input type for the generateCodeFromPrompt function.
 * - GenerateCodeFromPromptOutput - The return type for the generateCodeFromPrompt function.
 */

import {ai} from '@/ai/genkit';
import {z} from 'genkit';

const GenerateCodeFromPromptInputSchema = z.object({
  prompt: z.string().describe('A prompt describing the code to generate.'),
});
export type GenerateCodeFromPromptInput = z.infer<
  typeof GenerateCodeFromPromptInputSchema
>;

const GenerateCodeFromPromptOutputSchema = z.object({
  code: z.string().describe('The generated code.'),
});
export type GenerateCodeFromPromptOutput = z.infer<
  typeof GenerateCodeFromPromptOutputSchema
>;

export async function generateCodeFromPrompt(
  input: GenerateCodeFromPromptInput
): Promise<GenerateCodeFromPromptOutput> {
  return generateCodeFromPromptFlow(input);
}

const prompt = ai.definePrompt({
  name: 'generateCodeFromPromptPrompt',
  input: {schema: GenerateCodeFromPromptInputSchema},
  output: {schema: GenerateCodeFromPromptOutputSchema},
  prompt: `You are an expert code generator. Generate code based on the following prompt:\n\nPrompt: {{{prompt}}}`,
});

const generateCodeFromPromptFlow = ai.defineFlow(
  {
    name: 'generateCodeFromPromptFlow',
    inputSchema: GenerateCodeFromPromptInputSchema,
    outputSchema: GenerateCodeFromPromptOutputSchema,
  },
  async input => {
    const {output} = await prompt(input);
    return output!;
  }
);
