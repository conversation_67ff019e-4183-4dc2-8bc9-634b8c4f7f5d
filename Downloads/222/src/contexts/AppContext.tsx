
"use client";

import type { CodeFile, Plugin, AiActionType } from '@/types';
import React, { createContext, useContext, useState, ReactNode, useEffect, useCallback } from 'react';
import { useToast } from "@/hooks/use-toast";
import J<PERSON>Z<PERSON> from 'jszip';
import { generateFile, type GenerateFileOutput, type GenerateFileInput } from '@/services/ai-flows/generate-file';
import { explainCode, type ExplainCodeInput, type ExplainCodeOutput } from '@/services/ai-flows/explain-code';
import { improveCode, type ImproveCodeInput, type ImproveCodeOutput } from '@/services/ai-flows/improve-code';
import { getLocalOllamaModels } from '@/services/ollama';

interface AppContextType {
  files: CodeFile[];
  openFileIds: string[];
  activeFileId: string | null;
  activeFile: CodeFile | null;
  editorContent: string;
  setEditorContent: (content: string) => void;
  selectFile: (fileId: string | null) => void;
  closeFileTab: (fileIdToClose: string) => void;
  createFile: (name: string, type: 'file' | 'folder', parentId?: string | null, initialContent?: string) => CodeFile | undefined;
  deleteFile: (fileId: string) => void;
  saveActiveFile: () => void; // Persists all files to localStorage
  updateFileName: (fileId: string, newName: string) => void;
  updateFileContentById: (fileId: string, newContent: string, openFile?: boolean) => void;
  getFilePath: (fileId: string, allFiles: CodeFile[]) => string;

  directoryHandle: FileSystemDirectoryHandle | null;
  isFileSystemAccessSupported: boolean;
  promptAndLoadFromDirectory: () => Promise<boolean>;
  isLoadingFromDirectory: boolean;

  isInitialLoadDone: boolean;
  setIsInitialLoadDone: (done: boolean) => void;
  loadFilesFromLocalStorage: () => void;
  startNewInMemoryProject: () => void;

  downloadAllFilesAsZip: () => void;

  plugins: Plugin[];
  installedPlugins: string[];
  installPlugin: (pluginId: string) => void;
  uninstallPlugin: (pluginId: string) => void;

  isAiLoading: boolean;
  currentAiAction: AiActionType | null;
  aiFileResponse: GenerateFileOutput | null;
  aiExplanationResponse: ExplainCodeOutput | null;
  aiImprovementResponse: ImproveCodeOutput | null;

  runGenerateFileFlow: (input: GenerateFileInput) => Promise<void>;
  runExplainCodeFlow: (input: ExplainCodeInput) => Promise<void>;
  runImproveCodeFlow: (input: ImproveCodeInput) => Promise<void>;
  clearAiResponses: () => void;

  selectedText: string;
  setSelectedText: (text: string) => void;
  toast: ReturnType<typeof useToast>['toast'];

  availableOllamaModels: string[];
  loadAvailableOllamaModels: () => Promise<void>;
  selectedModel: string;
  setSelectedModel: (model: string) => void;

  isNetworkAccessEnabled: boolean;
  setIsNetworkAccessEnabled: (enabled: boolean) => void;
}

const AppContext = createContext<AppContextType | undefined>(undefined);

const initialPlugins: Plugin[] = [
  { id: 'plugin-1', name: '代码检查器 Pro', description: '高级代码检查功能，支持多种语言的静态分析。', version: '1.0.0', author: '开发工具公司' },
  { id: 'plugin-2', name: 'Git 集成 Plus', description: '增强的 Git 操作，包括图形化提交、分支和合并。', version: '1.2.0', author: '版本控制大师' },
  { id: 'plugin-3', name: '主题定制器', description: '自定义编辑器主题，支持导入 VS Code 主题。', version: '0.9.0', author: 'UI 专家' },
];

export const AppProvider = ({ children }: { children: ReactNode }) => {
  const [files, setFiles] = useState<CodeFile[]>([]);
  const [openFileIds, setOpenFileIds] = useState<string[]>([]);
  const [activeFileId, setActiveFileId] = useState<string | null>(null);
  const [editorContent, _setEditorContent] = useState<string>(''); 
  
  const [pluginsData] = useState<Plugin[]>(initialPlugins);
  const [installedPlugins, setInstalledPlugins] = useState<string[]>([]);
  const [selectedText, setSelectedText] = useState('');
  const { toast } = useToast();

  const [directoryHandle, setDirectoryHandle] = useState<FileSystemDirectoryHandle | null>(null);
  const [isFileSystemAccessSupported, setIsFileSystemAccessSupported] = useState(false);
  const [isLoadingFromDirectory, setIsLoadingFromDirectory] = useState(false);
  const [isInitialLoadDone, setIsInitialLoadDone] = useState(false);

  const [isAiLoading, setIsAiLoading] = useState(false);
  const [currentAiAction, setCurrentAiAction] = useState<AiActionType | null>(null);
  const [aiFileResponse, setAiFileResponse] = useState<GenerateFileOutput | null>(null);
  const [aiExplanationResponse, setAiExplanationResponse] = useState<ExplainCodeOutput | null>(null);
  const [aiImprovementResponse, setAiImprovementResponse] = useState<ImproveCodeOutput | null>(null);

  const [availableOllamaModels, setAvailableOllamaModels] = useState<string[]>([]);
  const [selectedModel, setSelectedModel] = useState<string>('llama3');
  const [isNetworkAccessEnabled, _setIsNetworkAccessEnabled] = useState<boolean>(false);


  const setIsNetworkAccessEnabled = useCallback((enabled: boolean) => {
    _setIsNetworkAccessEnabled(enabled);
    try {
      localStorage.setItem('ollamaCode_isNetworkAccessEnabled', JSON.stringify(enabled));
      toast({ title: "网络访问已 " + (enabled ? "启用" : "禁用"), duration: 3000 });
    } catch (e) {
      console.error("保存网络访问状态到 localStorage 失败:", e);
    }
  }, [toast]);


  const loadAvailableOllamaModels = useCallback(async () => {
    try {
      const models = await getLocalOllamaModels();
      setAvailableOllamaModels(models);
      if (models.length === 0 && isInitialLoadDone) { // Only toast if initial load is done
        toast({ 
          title: "未能加载本地 Ollama 模型",
          description: "请确保 Ollama 服务正在运行并且可以访问 (通常在 http://localhost:11434)。检查应用服务器控制台以获取更多错误详情。",
          variant: "destructive",
          duration: 10000, 
        });
      }
    } catch (error) {
      console.error("获取 Ollama 模型失败:", error);
      if (isInitialLoadDone) { // Only toast if initial load is done
        toast({ 
          title: "获取 Ollama 模型失败",
          description: (error as Error).message,
          variant: "destructive",
          duration: 10000, 
        });
      }
    }
  }, [isInitialLoadDone, toast]);

  useEffect(() => {
    setIsFileSystemAccessSupported(typeof window !== 'undefined' && 'showDirectoryPicker' in window);
    loadAvailableOllamaModels();
    try {
      const storedNetworkAccess = localStorage.getItem('ollamaCode_isNetworkAccessEnabled');
      if (storedNetworkAccess !== null) {
        _setIsNetworkAccessEnabled(JSON.parse(storedNetworkAccess));
      }
    } catch (e) {
      console.error("从 localStorage 加载网络访问状态失败:", e);
    }
  }, [loadAvailableOllamaModels]);

  const saveFilesToLocalStorage = useCallback((updatedFiles: CodeFile[]) => {
    try {
      localStorage.setItem('ollamaCode_files', JSON.stringify(updatedFiles));
      localStorage.setItem('ollamaCode_openFileIds', JSON.stringify(openFileIds));
      if (activeFileId) {
        localStorage.setItem('ollamaCode_activeFileId', activeFileId);
      } else {
        localStorage.removeItem('ollamaCode_activeFileId');
      }
    } catch (e) {
      console.error("保存文件到 localStorage 失败:", e);
      toast({title: "存储错误", description: "无法将文件保存到浏览器存储。可能是存储已满。", variant: "destructive"});
    }
  }, [toast, openFileIds, activeFileId]);

  const loadFilesFromLocalStorage = useCallback(() => {
    console.log("正在尝试从 localStorage 加载");
    try {
      const storedFiles = localStorage.getItem('ollamaCode_files');
      if (storedFiles) {
        const parsedFiles: CodeFile[] = JSON.parse(storedFiles);
        setFiles(parsedFiles);

        const storedOpenFileIds = localStorage.getItem('ollamaCode_openFileIds');
        if (storedOpenFileIds) {
          const parsedOpenFileIds: string[] = JSON.parse(storedOpenFileIds);
          const validOpenFileIds = parsedOpenFileIds.filter(id => parsedFiles.some(f => f.id === id && f.type === 'file'));
          setOpenFileIds(validOpenFileIds);

          const storedActiveFileId = localStorage.getItem('ollamaCode_activeFileId');
          if (storedActiveFileId && validOpenFileIds.includes(storedActiveFileId)) {
            setActiveFileId(storedActiveFileId);
            const activeFile = parsedFiles.find(f => f.id === storedActiveFileId);
            if (activeFile) _setEditorContent(activeFile.content);
          } else if (validOpenFileIds.length > 0) {
            const lastOpenFileId = validOpenFileIds[validOpenFileIds.length - 1];
            setActiveFileId(lastOpenFileId);
            const activeFile = parsedFiles.find(f => f.id === lastOpenFileId);
            if (activeFile) _setEditorContent(activeFile.content);
          }
        }
        if (parsedFiles.length > 0) {
            toast({ title: "项目已加载", description: "文件已从浏览器缓存加载。" });
        }
      }
      const storedInstalledPlugins = localStorage.getItem('ollamaCode_installedPlugins');
      if (storedInstalledPlugins) {
        setInstalledPlugins(JSON.parse(storedInstalledPlugins));
      }
    } catch (e) {
        console.error("从 localStorage 加载文件失败:", e);
        toast({title: "加载错误", description: "无法从浏览器存储中加载文件。缓存可能已损坏。", variant: "destructive"});
        localStorage.removeItem('ollamaCode_files');
        localStorage.removeItem('ollamaCode_openFileIds');
        localStorage.removeItem('ollamaCode_activeFileId');
    }
    setIsInitialLoadDone(true);
  }, [toast]);
  
  const setEditorContent = (content: string) => {
    _setEditorContent(content);
    if (activeFileId) {
      setFiles(prevFiles => 
        prevFiles.map(f => 
          f.id === activeFileId ? { ...f, content } : f
        )
      );
    }
  };

  const selectFile = (fileId: string | null) => {
    if (fileId === null) {
      setActiveFileId(null);
      _setEditorContent('');
      return;
    }

    const fileToSelect = files.find(f => f.id === fileId);
    if (fileToSelect && fileToSelect.type === 'file') {
      if (!openFileIds.includes(fileId)) {
        setOpenFileIds(prevOpenFileIds => [...prevOpenFileIds, fileId]);
      }
      setActiveFileId(fileId);
      _setEditorContent(fileToSelect.content);
    } else if (!fileToSelect && activeFileId === fileId) { 
        setActiveFileId(null);
        _setEditorContent('');
    } else if (fileToSelect && fileToSelect.type === 'folder') {
      // Optionally expand folder or do nothing
    }
  };

  const closeFileTab = (fileIdToClose: string) => {
    setOpenFileIds(prevOpenFileIds => {
      const newOpenFileIds = prevOpenFileIds.filter(id => id !== fileIdToClose);
      if (activeFileId === fileIdToClose) {
        if (newOpenFileIds.length > 0) {
          selectFile(newOpenFileIds[newOpenFileIds.length - 1]); 
        } else {
          selectFile(null); 
        }
      }
      return newOpenFileIds;
    });
  };


  const startNewInMemoryProject = useCallback(() => {
    setFiles([]);
    setOpenFileIds([]);
    setActiveFileId(null);
    _setEditorContent('');
    setDirectoryHandle(null);
    clearAiResponses();
    localStorage.removeItem('ollamaCode_files');
    localStorage.removeItem('ollamaCode_openFileIds');
    localStorage.removeItem('ollamaCode_activeFileId');
    toast({ title: "新项目已开始", description: "您可以开始创建文件和文件夹了。" });
    setIsInitialLoadDone(true);
  }, [toast]);

  const processDirectory = async (dirHandle: FileSystemDirectoryHandle, parentId: string | null, pathPrefix: string = ''): Promise<CodeFile[]> => {
    const loadedFiles: CodeFile[] = [];
    console.log(`正在处理目录: ${pathPrefix || dirHandle.name}`);
    try {
        for await (const entry of (dirHandle as any).values()) {
            console.log(`找到条目: ${pathPrefix}${entry.name}, 类型: ${entry.kind}`);
            const newId = Date.now() + '-' + Math.random().toString(36).substring(2, 9);
            if (entry.kind === 'file') {
                if (entry.name.includes('.') && !entry.name.match(/\.(txt|js|ts|tsx|jsx|json|md|html|css|py|java|c|cpp|go|rs|rb|php|swift|kt|env|yaml|yml|sh|ini|xml|svg)$/i)) {
                    console.log(`正在跳过非文本类文件: ${pathPrefix}${entry.name}`);
                    continue;
                }
                try {
                    const fileHandle = await dirHandle.getFileHandle(entry.name);
                    const file = await fileHandle.getFile();
                    const content = await file.text();
                    loadedFiles.push({
                    id: newId,
                    name: entry.name,
                    content: content,
                    parentId: parentId,
                    type: 'file',
                    language: entry.name.split('.').pop() || 'plaintext',
                    });
                    console.log(`已加载文件: ${pathPrefix}${entry.name}`);
                } catch (e) {
                    console.error(`读取文件 ${pathPrefix}${entry.name} 时出错:`, e);
                    loadedFiles.push({
                        id: newId,
                        name: entry.name + " (读取错误)",
                        content: `// 无法读取文件内容: ${(e as Error).message}`,
                        parentId: parentId,
                        type: 'file',
                        language: 'plaintext',
                    });
                }
            } else if (entry.kind === 'directory') {
                if (['.git', 'node_modules', '.vscode', '.next', 'dist', 'build', '.DS_Store'].includes(entry.name)) {
                    console.log(`正在跳过目录: ${pathPrefix}${entry.name}`);
                    continue;
                }
                const folderFile: CodeFile = {
                id: newId,
                name: entry.name,
                content: '',
                parentId: parentId,
                type: 'folder',
                };
                loadedFiles.push(folderFile);
                console.log(`已添加文件夹: ${pathPrefix}${entry.name}, 正在创建子进程。`);
                try {
                    const subDirectoryHandle = await dirHandle.getDirectoryHandle(entry.name);
                    const subFiles = await processDirectory(subDirectoryHandle, newId, `${pathPrefix}${entry.name}/`);
                    loadedFiles.push(...subFiles);
                } catch (e) {
                    console.error(`无法访问子目录 ${pathPrefix}${entry.name}:`, e);
                }
            }
        }
    } catch (e) {
        console.error(`处理目录 ${pathPrefix || dirHandle.name} 中的条目时出错:`, e);
        toast({title: "目录处理错误", description: `处理目录 "${pathPrefix || dirHandle.name}" 中的条目时出错: ${(e as Error).message}`, variant: "destructive"})
    }
    console.log(`完成处理目录: ${pathPrefix || dirHandle.name}, 本次调用直接加载了 ${loadedFiles.length} 个项目。`);
    return loadedFiles;
  };

  const promptAndLoadFromDirectory = async (): Promise<boolean> => {
    if (!isFileSystemAccessSupported) {
      toast({ title: "功能不支持", description: "您的浏览器不支持文件系统访问 API。", variant: "destructive" });
      return false;
    }
    setIsLoadingFromDirectory(true);
    try {
      const handle = await (window as any).showDirectoryPicker();
      setDirectoryHandle(handle);
      setFiles([]); 
      setOpenFileIds([]);
      setActiveFileId(null);
      _setEditorContent('');
      clearAiResponses();
      
      const loadedFiles = await processDirectory(handle, null);
      setFiles(loadedFiles);
      saveFilesToLocalStorage(loadedFiles);

      if (loadedFiles.length > 0) {
        toast({ title: "文件夹已加载", description: `已加载来自 "${handle.name}" 的文件。` });
      } else {
        toast({ title: "文件夹为空或无匹配文件", description: `文件夹 "${handle.name}" 已处理，但未找到可加载的文件。`, variant: "default" });
      }
      return true; 
    } catch (error) {
      const domError = error as DOMException;
      if (domError.name === 'AbortError') {
        toast({ title: "操作已取消", description: "文件夹选择已取消。", variant: "default" });
        return false; 
      } 
      const errorMessage = (error as Error).message?.toLowerCase() || '';
      if (errorMessage.includes("cross origin") || errorMessage.includes("file picker") || errorMessage.includes("cross-origin") || errorMessage.includes("sandboxed")) {
        toast({
          title: "加载失败",
          description: "无法打开本地文件夹选择器。若您在类似 Firebase Studio 的嵌入式环境(iframe)中使用本应用，浏览器安全策略可能会阻止此操作。请尝试在独立的浏览器标签页中运行本应用以使用此功能，或检查您的环境设置。",
          variant: "destructive",
          duration: 10000
        });
      } else {
        toast({ title: "加载失败", description: `无法加载文件夹: ${(error as Error).message}`, variant: "destructive" });
      }
      console.error("加载目录时出错:", error);
      return true; 
    } finally {
      setIsLoadingFromDirectory(false);
      // setIsInitialLoadDone(true); // This line was moved to page.tsx based on return value
    }
  };

  const saveInstalledPluginsToLocalStorage = useCallback((updatedInstalledPlugins: string[]) => {
    try {
        localStorage.setItem('ollamaCode_installedPlugins', JSON.stringify(updatedInstalledPlugins));
    } catch (e) {
        console.error("保存已安装插件到 localStorage 失败:", e);
    }
  }, []);

  const createFile = (name: string, type: 'file' | 'folder', parentId: string | null = null, initialContent?: string): CodeFile | undefined => {
    if (!name || !name.trim()) {
      toast({ title: "创建失败", description: "名称不能为空。", variant: "destructive" });
      return undefined;
    }
    const newFile: CodeFile = {
      id: `${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
      name: name.trim(),
      content: type === 'file' ? (initialContent !== undefined ? initialContent : `// ${name.trim()}\n`) : '',
      parentId,
      type,
      language: type === 'file' ? name.trim().split('.').pop() || 'plaintext' : undefined
    };
    const updatedFiles = [...files, newFile];
    setFiles(updatedFiles);
    saveFilesToLocalStorage(updatedFiles);
    if (type === 'file') {
      selectFile(newFile.id);
    }
    toast({ title: `${type === 'file' ? '文件' : '文件夹'}已创建`, description: name.trim() });
    return newFile;
  };

  const saveActiveFile = () => { 
    saveFilesToLocalStorage(files);
    const activeFileObj = files.find(f => f.id === activeFileId);
    if (activeFileObj) {
      toast({ title: "文件已保存", description: `更改已同步到浏览器缓存：${activeFileObj.name}` });
    } else {
      toast({ title: "文件已保存", description: "所有更改已同步到浏览器缓存。" });
    }
    if (directoryHandle) {
      toast({ title: "提示", description: "更改已保存到浏览器缓存，但尚未写回您的本地文件系统。", variant: "default", duration: 7000});
    }
  };

  const deleteFile = (fileId: string) => {
    const fileToDelete = files.find(f => f.id === fileId);
    if (!fileToDelete) return;

    let filesToDeleteIds = [fileId];
    if (fileToDelete.type === 'folder') {
      const findChildren = (parentId: string) => {
        const children = files.filter(f => f.parentId === parentId);
        children.forEach(child => {
          filesToDeleteIds.push(child.id);
          if (child.type === 'folder') {
            findChildren(child.id);
          }
        });
      };
      findChildren(fileId);
    }

    const updatedFiles = files.filter(f => !filesToDeleteIds.includes(f.id));
    setFiles(updatedFiles);
    
    const newOpenFileIds = openFileIds.filter(id => !filesToDeleteIds.includes(id));
    setOpenFileIds(newOpenFileIds);

    if (filesToDeleteIds.includes(activeFileId || '')) {
      if (newOpenFileIds.length > 0) {
        selectFile(newOpenFileIds[newOpenFileIds.length - 1]);
      } else {
        selectFile(null);
      }
    }
    
    saveFilesToLocalStorage(updatedFiles);
    toast({ title: `${fileToDelete.type === 'file' ? '文件' : '文件夹'}已删除`, description: fileToDelete.name, variant: "destructive" });
  };

  const updateFileName = (fileId: string, newName: string) => {
    if (!newName || !newName.trim()) {
      toast({ title: "重命名失败", description: "文件名不能为空。", variant: "destructive" });
      return;
    }
    const updatedFiles = files.map(f =>
      f.id === fileId ? { ...f, name: newName.trim(), language: f.type === 'file' ? newName.trim().split('.').pop() || 'plaintext' : undefined } : f
    );
    setFiles(updatedFiles);
    saveFilesToLocalStorage(updatedFiles);
    toast({ title: "文件已重命名", description: newName.trim() });
  };

  const updateFileContentById = (fileId: string, newContent: string, openFile: boolean = true) => {
    const updatedFiles = files.map(f =>
      f.id === fileId ? { ...f, content: newContent } : f
    );
    setFiles(updatedFiles);
    saveFilesToLocalStorage(updatedFiles);
    
    if (openFile) {
        selectFile(fileId); 
    } else if (activeFileId === fileId) {
        _setEditorContent(newContent); 
    }
  };

  const getFilePath = useCallback((fileId: string, allFiles: CodeFile[]): string => {
    const file = allFiles.find(f => f.id === fileId);
    if (!file) return '';
    let path = file.name;
    let currentParentId = file.parentId;
    const parentFolders: CodeFile[] = [];
    while (currentParentId) {
      const parentFolder = allFiles.find(f => f.id === currentParentId && f.type === 'folder');
      if (parentFolder) {
        parentFolders.unshift(parentFolder);
        currentParentId = parentFolder.parentId;
      } else {
        break;
      }
    }
    return parentFolders.map(pf => pf.name).join('/') + (parentFolders.length > 0 ? '/' : '') + path;
  }, []);

  const downloadAllFilesAsZip = useCallback(async () => {
    if (files.length === 0) {
      toast({ title: "无文件", description: "项目中没有可下载的文件。", variant: "destructive" });
      return;
    }
    const projectFiles = files.filter(f => f.type === 'file');
    if (projectFiles.length === 0) {
        toast({ title: "无文件", description: "项目中没有可打包的文件（只有文件夹）。", variant: "destructive" });
        return;
    }
    const zip = new JSZip();
    projectFiles.forEach(file => {
      const filePath = getFilePath(file.id, files);
      zip.file(filePath, file.content);
    });
    try {
      const zipContent = await zip.generateAsync({ type: "blob" });
      const link = document.createElement('a');
      link.href = URL.createObjectURL(zipContent);
      link.download = "ollama_code_project.zip";
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(link.href);
      toast({ title: "下载成功", description: "项目文件已开始下载。" });
    } catch (error) {
      console.error("ZIP 生成失败:", error);
      toast({ title: "下载失败", description: `无法创建 ZIP 文件：${(error as Error).message}`, variant: "destructive" });
    }
  }, [files, toast, getFilePath]);

  const installPlugin = (pluginId: string) => {
    if (!installedPlugins.includes(pluginId)) {
      const updatedInstalledPlugins = [...installedPlugins, pluginId];
      setInstalledPlugins(updatedInstalledPlugins);
      saveInstalledPluginsToLocalStorage(updatedInstalledPlugins);
      const plugin = pluginsData.find(p => p.id === pluginId);
      toast({ title: "插件已安装", description: plugin?.name });
    }
  };

  const uninstallPlugin = (pluginId: string) => {
    const updatedInstalledPlugins = installedPlugins.filter(id => id !== pluginId);
    setInstalledPlugins(updatedInstalledPlugins);
    saveInstalledPluginsToLocalStorage(updatedInstalledPlugins);
    const plugin = pluginsData.find(p => p.id === pluginId);
    toast({ title: "插件已卸载", description: plugin?.name, variant: "destructive" });
  };

  const clearAiResponses = () => {
    setAiFileResponse(null);
    setAiExplanationResponse(null);
    setAiImprovementResponse(null);
    setCurrentAiAction(null);
  };

  const runGenerateFileFlow = async (input: GenerateFileInput) => {
    setIsAiLoading(true);
    setCurrentAiAction('generateFile');
    setAiExplanationResponse(null);
    setAiImprovementResponse(null);
    try {
      const response = await generateFile(input);
      setAiFileResponse(response);
    } catch (error) {
      console.error("AI generateFileFlow error:", error);
      toast({ title: "AI 文件生成失败", description: (error as Error).message, variant: "destructive" });
      setAiFileResponse({
        fileName: input.currentFileName || "error.txt",
        fileContent: `// 生成文件时出错: ${(error as Error).message}`,
        explanation: `尝试生成文件时发生错误。`
      });
    } finally {
      setIsAiLoading(false);
    }
  };

  const runExplainCodeFlow = async (input: ExplainCodeInput) => {
    setIsAiLoading(true);
    setCurrentAiAction('explainCode');
    setAiFileResponse(null);
    setAiImprovementResponse(null);
    try {
      const response = await explainCode(input);
      setAiExplanationResponse(response);
    } catch (error) {
      console.error("AI explainCodeFlow error:", error);
      toast({ title: "AI 代码解释失败", description: (error as Error).message, variant: "destructive" });
      setAiExplanationResponse({ explanation: `解释代码时出错: ${(error as Error).message}` });
    } finally {
      setIsAiLoading(false);
    }
  };

  const runImproveCodeFlow = async (input: ImproveCodeInput) => {
    setIsAiLoading(true);
    setCurrentAiAction('improveCode');
    setAiFileResponse(null);
    setAiExplanationResponse(null);
    try {
      const response = await improveCode(input);
      setAiImprovementResponse(response);
    } catch (error) {
      console.error("AI improveCodeFlow error:", error);
      toast({ title: "AI 代码改进失败", description: (error as Error).message, variant: "destructive" });
      setAiImprovementResponse({
        improvedCode: input.code,
        explanation: `改进代码建议时出错: ${(error as Error).message}`
      });
    } finally {
      setIsAiLoading(false);
    }
  };

  // Compute activeFile
  const activeFile = activeFileId ? files.find(f => f.id === activeFileId) || null : null;

  return (
    <AppContext.Provider value={{
      files, openFileIds, activeFileId, activeFile, editorContent, setEditorContent, selectFile, closeFileTab,
      createFile, deleteFile, saveActiveFile, updateFileName, updateFileContentById, getFilePath,
      directoryHandle, isFileSystemAccessSupported, promptAndLoadFromDirectory, isLoadingFromDirectory,
      isInitialLoadDone, setIsInitialLoadDone, loadFilesFromLocalStorage, startNewInMemoryProject,
      downloadAllFilesAsZip,
      plugins: pluginsData,
      installedPlugins, installPlugin, uninstallPlugin,

      isAiLoading, currentAiAction,
      aiFileResponse, aiExplanationResponse, aiImprovementResponse,
      runGenerateFileFlow, runExplainCodeFlow, runImproveCodeFlow, clearAiResponses,

      selectedText, setSelectedText,
      toast,
      availableOllamaModels, loadAvailableOllamaModels, selectedModel, setSelectedModel,
      isNetworkAccessEnabled, setIsNetworkAccessEnabled
    }}>
      {children}
    </AppContext.Provider>
  );
};

export const useAppContext = () => {
  const context = useContext(AppContext);
  if (context === undefined) {
    throw new Error('useAppContext 必须在 AppProvider 中使用');
  }
  return context;
};

