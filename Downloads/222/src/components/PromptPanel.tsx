
"use client";

import React, { useState, useRef, useEffect, ChangeEvent } from 'react';
import { useAppContext } from '@/contexts/AppContext';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Loader2, Sparkles, SendHorizonal, Trash2, CheckCircle, AlertTriangle, Paperclip, Pause, Globe, AtSign, Code2, BookText, GitMerge, MessageSquareText, MousePointerSquareDashed, TerminalSquare, History, Globe2 } from 'lucide-react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from '@/components/ui/alert-dialog';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import type { GenerateFileInput } from '@/ai/flows/generate-file-flow';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger, DropdownMenuSeparator } from '@/components/ui/dropdown-menu';


export function PromptPanel() {
  const {
    isGenkitLoading,
    currentAiAction,
    genkitFileResponse,
    genkitExplanationResponse,
    genkitImprovementResponse,
    runGenerateFileFlow,
    clearGenkitResponses,
    toast,
    createFile,
    updateFileContentById,
    files,
    activeFile,
    selectedText, 
    availableOllamaModels,
    selectedModel,      
    setSelectedModel,
    isNetworkAccessEnabled,
    setIsNetworkAccessEnabled,   
    runExplainCodeFlow, 
    runImproveCodeFlow, 
  } = useAppContext();

  const [currentPrompt, setCurrentPrompt] = useState('');
  const extractedMention = useRef<{fileName: string, fileId: string, fileContent: string} | null>(null);
  
  const [showOverwriteDialog, setShowOverwriteDialog] = useState(false);
  const [fileToOverwriteDetails, setFileToOverwriteDetails] = useState<{ name: string; id: string; content: string } | null>(null);
  
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [fileDataUri, setFileDataUri] = useState<string | null>(null);


  const handlePauseClick = () => {
    console.log("Pause button clicked. Functionality to interrupt Genkit flow not yet implemented.");
    toast({ title: "暂停功能", description: "暂停 AI 生成的功能正在开发中。", duration: 3000 });
  };

  const handleFileSelect = (event: ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      if (!file.type.startsWith('image/')) {
        toast({ title: "文件类型无效", description: "请选择一个图片文件。", variant: "destructive" });
        if (event.target) event.target.value = ""; 
        return;
      }
      const reader = new FileReader();
      reader.onload = (e) => {
        setFileDataUri(e.target?.result as string);
        toast({ title: "图片已加载", description: `已加载 ${file.name}。Genkit 文件生成流程当前不直接使用此图片。` });
      };
      reader.onerror = () => {
        toast({ title: "图片加载失败", description: "读取文件时发生错误。", variant: "destructive" });
      }
      reader.readAsDataURL(file);
    }
  };

  const clearUploadedFile = () => {
    setFileDataUri(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = ""; 
    }
    toast({ title: "上传已清除", description: "已移除选中的图片。" });
  };

  const handleSendPrompt = async () => {
    if (!currentPrompt.trim()) {
       toast({ title: "输入为空", description: "请输入您的需求以生成或修改文件。", variant: "destructive" });
      return;
    }

    extractedMention.current = null; 
    
    const mentionRegex = /@([\w.-]+\.\w+)/;
    const match = currentPrompt.match(mentionRegex);
    let inputForFlow: GenerateFileInput = { 
        prompt: currentPrompt,
    };

    if (match && match[1]) {
      const mentionedFileName = match[1];
      const targetFile = files.find(f => f.name === mentionedFileName && f.type === 'file');
      if (targetFile) {
        extractedMention.current = { fileName: targetFile.name, fileId: targetFile.id, fileContent: targetFile.content };
        inputForFlow.currentFileName = targetFile.name;
        inputForFlow.currentFileContent = targetFile.content;
        toast({title: "分析文件", description: `正在分析 @${targetFile.name} 以进行修改...`});
      } else {
        toast({ title: "文件未找到", description: `未在项目中找到文件 "@${mentionedFileName}"。将尝试生成新文件。`, variant: "default" });
      }
    }
    await runGenerateFileFlow(inputForFlow);
  };
  
  const handleAcceptGeneratedFile = () => {
    if (!genkitFileResponse) return;

    const { fileName, fileContent } = genkitFileResponse;
    const existingFile = files.find(f => f.name === fileName && f.type === 'file');

    if (existingFile) {
      if (extractedMention.current && extractedMention.current.fileName === fileName && extractedMention.current.id === existingFile.id) {
        updateFileContentById(existingFile.id, fileContent, true);
        toast({ title: "文件已更新", description: `${fileName} 的内容已成功更新。` });
        clearPromptAndGenkitStateLocal();
      } else {
         setFileToOverwriteDetails({ name: fileName, id: existingFile.id, content: fileContent });
         setShowOverwriteDialog(true);
      }
    } else {
      const newFile = createFile(fileName, 'file', null, fileContent);
      if (newFile) {
        toast({ title: "文件已创建", description: `文件 "${fileName}" 已成功创建并打开。` });
      }
      clearPromptAndGenkitStateLocal();
    }
  };

  const confirmFileOverwrite = () => {
    if (fileToOverwriteDetails) {
      updateFileContentById(fileToOverwriteDetails.id, fileToOverwriteDetails.content, true);
      toast({ title: "文件已覆盖", description: `${fileToOverwriteDetails.name} 已被新内容覆盖。`});
    }
    setShowOverwriteDialog(false);
    setFileToOverwriteDetails(null);
    clearPromptAndGenkitStateLocal();
  };
  
  const clearPromptAndGenkitStateLocal = () => {
    setCurrentPrompt('');
    clearGenkitResponses();
    extractedMention.current = null;
    setFileDataUri(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  const handleContextMenuItemClick = (action: string) => {
    switch (action) {
      case '@Add Context':
        setCurrentPrompt(prev => prev + "@");
        // Focus the textarea after adding @
        const textarea = document.querySelector('.h-24.text-sm.resize-none') as HTMLTextAreaElement | null;
        textarea?.focus();
        break;
      default:
        toast({ title: "功能即将推出", description: `"${action}" 功能正在开发中。`, duration: 3000 });
        break;
    }
  };
  
 const renderResponseArea = () => {
    if (isGenkitLoading) {
      let loadingMessage = "AI 正在处理您的请求...";
      if (currentAiAction === 'generateFile' && extractedMention.current) {
        loadingMessage += `\n正在分析 @${extractedMention.current.fileName} ...`;
      } else if ((currentAiAction === 'explainCode' || currentAiAction === 'improveCode')) {
        const targetName = activeFile ? activeFile.name : (selectedText ? "选定代码" : "代码");
        loadingMessage += `\n正在分析 ${targetName}...`;
      }
      return (
        <div className="flex flex-col items-center justify-center h-full text-center p-4">
          <Loader2 size={24} className="animate-spin text-muted-foreground" />
          <p className="ml-2 mt-2 text-muted-foreground whitespace-pre-wrap">{loadingMessage}</p>
        </div>
      );
    }

    let responseTitle: string | null = null;
    let responseFileContent: string | null = null;
    let explanationText: string | null = null;
    let showAcceptButton = false;
    let markdownContentToDisplay : string | null = null;


    if (currentAiAction === 'generateFile' && genkitFileResponse) {
        responseTitle = genkitFileResponse.fileName;
        responseFileContent = genkitFileResponse.fileContent;
        explanationText = genkitFileResponse.explanation || null;
        showAcceptButton = true;
    } else if (currentAiAction === 'explainCode' && genkitExplanationResponse) {
        responseTitle = "代码解释";
        markdownContentToDisplay = genkitExplanationResponse.explanation;
    } else if (currentAiAction === 'improveCode' && genkitImprovementResponse) {
        responseTitle = "代码改进建议";
        markdownContentToDisplay = `**建议的代码:**\n\`\`\`\n${genkitImprovementResponse.improvedCode || "无代码建议。"}\n\`\`\`\n\n**解释:**\n${genkitImprovementResponse.explanation}`;
    }
    
    if (explanationText || responseFileContent || markdownContentToDisplay) {
      return (
        <div className="bg-background p-3 rounded-md shadow space-y-3">
          {explanationText && (
              <div className="prose prose-sm dark:prose-invert max-w-none p-2 border-l-2 border-accent mb-2">
                  <ReactMarkdown remarkPlugins={[remarkGfm]}>{explanationText}</ReactMarkdown>
              </div>
          )}
          {responseTitle && responseFileContent && (
            <div className="border border-border rounded-md">
              <div className="flex items-center justify-between bg-muted px-3 py-2 border-b border-border">
                  <Badge variant="secondary" className="text-sm px-2 py-0.5 font-mono">{responseTitle}</Badge>
                  {showAcceptButton && (
                      <Button onClick={handleAcceptGeneratedFile} size="sm" variant="default" className="h-7 px-2 py-1">
                          <CheckCircle size={14} className="mr-1.5"/> 接受
                      </Button>
                  )}
              </div>
              <div className="p-2 max-h-[40vh] overflow-y-auto">
                <ReactMarkdown
                    className="prose prose-sm dark:prose-invert max-w-none"
                    remarkPlugins={[remarkGfm]}
                    components={{
                        pre({node, ...props}) { return <pre {...props} className="bg-muted/50 p-2 rounded-md !text-xs"/> },
                        code({node, inline, className, children, ...props}) {
                        const match = /language-(\w+)/.exec(className || '')
                        return !inline && match ? (
                            <code {...props} className={`language-${match[1]} !text-xs`}>{children}</code>
                        ) : (
                            <code {...props} className="!text-xs before:content-[''] after:content-[''] bg-muted-foreground/10 px-1 py-0.5 rounded">{children}</code>
                        )
                        }
                    }}
                >
                    {responseFileContent}
                </ReactMarkdown>
              </div>
            </div>
          )}
           {markdownContentToDisplay && !responseFileContent && ( 
             <ReactMarkdown
                className="prose prose-sm dark:prose-invert max-w-none"
                remarkPlugins={[remarkGfm]}
                components={{
                    pre({node, ...props}) { return <pre {...props} className="bg-muted p-2 rounded-md !text-xs"/> },
                    code({node, inline, className, children, ...props}) {
                    const match = /language-(\w+)/.exec(className || '')
                    return !inline && match ? (
                        <code {...props} className={`language-${match[1]} !text-xs`}>{children}</code>
                    ) : (
                        <code {...props} className="!text-xs before:content-[''] after:content-[''] bg-muted-foreground/10 px-1 py-0.5 rounded">{children}</code>
                    )
                    }
                }}
            >
                {markdownContentToDisplay}
            </ReactMarkdown>
           )}
        </div>
      );
    }
    
    return <div className="flex items-center justify-center h-full text-muted-foreground/70 p-4 text-center">描述您的需求，例如：“创建一个 React 登录表单组件”或“使用 @filename.tsx 更新按钮组件”。</div>;
  };


  return (
    <div className="h-full flex flex-col bg-background border-l border-border">
      <div className="p-3 border-b border-border bg-[hsl(var(--card))] flex items-center justify-between text-sm font-medium rounded-none">
        <div className="flex items-center">
           <Sparkles size={16} className="mr-2 text-primary"/> AI 助手 
        </div>
        <Button 
            variant="ghost" 
            size="icon" 
            onClick={clearPromptAndGenkitStateLocal} 
            disabled={isGenkitLoading || (!genkitFileResponse && !genkitExplanationResponse && !genkitImprovementResponse && !currentPrompt && !fileDataUri)} 
            title="清空提示和响应" 
            className="h-7 w-7"
        >
            <Trash2 size={14}/>
        </Button>
      </div>
      
      <ScrollArea className="flex-grow bg-[hsl(var(--muted))] min-h-0 p-2">
        {renderResponseArea()}
      </ScrollArea>
    
      <div className="p-3 border-t border-border">
        <Textarea
            value={currentPrompt}
            onChange={(e) => setCurrentPrompt(e.target.value)}
            placeholder="描述您想生成或修改的文件... 使用 @文件名.扩展名 来指定要修改的文件。"
            className="h-24 text-sm resize-none bg-[hsl(var(--input))] focus-visible:ring-accent"
            disabled={isGenkitLoading}
            onKeyDown={(e) => {
                if (e.key === 'Enter' && (e.metaKey || e.ctrlKey)) {
                e.preventDefault();
                handleSendPrompt();
                }
            }}
        />
        <div className="mt-2 flex items-center justify-between">
            <div className="flex items-center gap-2"> {/* Left-aligned controls */}
               <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="outline" size="icon" disabled={isGenkitLoading} className="h-9 w-9 bg-[hsl(var(--input))]" title="添加上下文">
                      <AtSign size={16} />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="start">
                    <DropdownMenuItem onClick={() => handleContextMenuItemClick('Code')}>
                      <Code2 className="mr-2 h-4 w-4" />
                      <span>Code</span>
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => handleContextMenuItemClick('Docs')}>
                      <BookText className="mr-2 h-4 w-4" />
                      <span>Docs</span>
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => handleContextMenuItemClick('Git')}>
                      <GitMerge className="mr-2 h-4 w-4" />
                      <span>Git</span>
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => handleContextMenuItemClick('Past Chats')}>
                      <MessageSquareText className="mr-2 h-4 w-4" />
                      <span>Past Chats</span>
                    </DropdownMenuItem>
                     <DropdownMenuItem onClick={() => handleContextMenuItemClick('Cursor Rules')}>
                      <MousePointerSquareDashed className="mr-2 h-4 w-4" />
                      <span>Cursor Rules</span>
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => handleContextMenuItemClick('Terminals')}>
                      <TerminalSquare className="mr-2 h-4 w-4" />
                      <span>Terminals</span>
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem onClick={() => handleContextMenuItemClick('Linter Errors')}>
                      <AlertTriangle className="mr-2 h-4 w-4" />
                      <span>Linter Errors</span>
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => handleContextMenuItemClick('Web')}>
                      <Globe2 className="mr-2 h-4 w-4" />
                      <span>Web</span>
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => handleContextMenuItemClick('Recent Changes')}>
                      <History className="mr-2 h-4 w-4" />
                      <span>Recent Changes</span>
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem onClick={() => handleContextMenuItemClick('@Add Context')}>
                      <AtSign className="mr-2 h-4 w-4" />
                      <span>@Add Context</span>
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
                <Button variant="outline" size="icon" onClick={() => fileInputRef.current?.click()} disabled={isGenkitLoading} className="h-9 w-9 bg-[hsl(var(--input))]">
                    <Paperclip size={16} />
                </Button>
                <input type="file" ref={fileInputRef} onChange={handleFileSelect} className="hidden" accept="image/*" />
            </div>

            <div className="flex items-center gap-2"> {/* Right-aligned controls */}
                <div className="flex items-center space-x-2 bg-[hsl(var(--input))] p-1.5 rounded-md">
                  <Switch
                    id="network-access"
                    checked={isNetworkAccessEnabled}
                    onCheckedChange={setIsNetworkAccessEnabled}
                    disabled={isGenkitLoading}
                  />
                  <Label htmlFor="network-access" className="text-xs cursor-pointer flex items-center gap-1">
                    <Globe size={14} /> 网络
                  </Label>
                </div>
                 <Select value={selectedModel} onValueChange={setSelectedModel} disabled={isGenkitLoading}>
                    <SelectTrigger className="w-[180px] h-9 text-xs bg-[hsl(var(--input))]">
                        <SelectValue placeholder="选择一个模型" />
                    </SelectTrigger>
                    <SelectContent>
                        {availableOllamaModels.length > 0 ? (
                            availableOllamaModels.map(modelName => (
                                <SelectItem key={modelName} value={modelName} className="text-xs">
                                    {modelName}
                                </SelectItem>
                            ))
                        ) : (
                            <SelectItem value="no-models" disabled className="text-xs">无 Ollama 模型</SelectItem>
                        )}
                         <SelectItem value="genkit-default" className="text-xs font-semibold">Genkit 使用其配置模型</SelectItem>
                    </SelectContent>
                </Select>
                <Button variant="outline" size="icon" onClick={handlePauseClick} disabled={!isGenkitLoading} className="h-9 w-9 bg-[hsl(var(--input))]" title="暂停生成">
                    <Pause size={16} />
                </Button>
                <Button onClick={handleSendPrompt} disabled={isGenkitLoading || !currentPrompt.trim()} className="h-9 text-sm">
                    {isGenkitLoading ? <Loader2 size={18} className="animate-spin" /> : <SendHorizonal size={18}/>}
                    发送
                </Button>
            </div>
        </div>
        {fileDataUri && (
          <div className="mt-2 text-xs text-muted-foreground flex items-center gap-1">
            <img src={fileDataUri} alt="Uploaded preview" className="max-h-8 max-w-[50px] rounded-sm border border-border" />
            <span>图片已加载。</span>
            <Button variant="ghost" size="icon" onClick={clearUploadedFile} className="h-6 w-6 ml-1" title="清除已上传的图片">
                <Trash2 size={12} className="text-destructive"/>
            </Button>
          </div>
        )}
      </div>

      <AlertDialog open={showOverwriteDialog} onOpenChange={setShowOverwriteDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle className="flex items-center">
              <AlertTriangle size={20} className="mr-2 text-destructive" />
              文件已存在
            </AlertDialogTitle>
            <AlertDialogDescription>
              文件 "{fileToOverwriteDetails?.name}" 已存在。您确定要覆盖它吗？此操作无法撤销。
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={() => { setShowOverwriteDialog(false); setFileToOverwriteDetails(null); }}>取消</AlertDialogCancel>
            <AlertDialogAction onClick={confirmFileOverwrite} className="bg-destructive hover:bg-destructive/90">覆盖</AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}


    

    