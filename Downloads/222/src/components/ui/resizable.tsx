
"use client"

import * as React from "react"
import { GripVertical } from "lucide-react"
import {
  PanelGroupContext,
  PanelGroupContextType,
  PanelResizeHandle as ResizeHandlePrimitive,
  PanelResizeHandleProps as ResizeHandlePrimitiveProps,
  Panel as ResizablePanelPrimitive,
  PanelGroup as ResizablePanelGroupPrimitive,
  PanelGroupProps as ResizablePanelGroupPrimitiveProps,
  PanelProps as ResizablePanelPrimitiveProps,
  PanelResizeHandle,
} from "react-resizable-panels"

import { cn } from "@/lib/utils"

const ResizablePanelGroup = React.forwardRef<
  React.ElementRef<typeof ResizablePanelGroupPrimitive>,
  React.ComponentProps<typeof ResizablePanelGroupPrimitive>
>(({ className, ...props }, ref) => (
  <ResizablePanelGroupPrimitive
    ref={ref}
    className={cn(
      "flex h-full w-full data-[panel-group-direction=vertical]:flex-col",
      className
    )}
    {...props}
  />
))
ResizablePanelGroup.displayName = "ResizablePanelGroup"

const ResizablePanel = ResizablePanelPrimitive

const panelResizeHandleContext = React.createContext<{
  id?: string
  isFocused?: boolean
  dragHandleProps?: PanelResizeHandle["props"]
  value?: number
} | null>(null)

function usePanelResizeHandle() {
  const context = React.useContext(panelResizeHandleContext)

  if (!context) {
    throw new Error(
      "usePanelResizeHandle must be used within a PanelResizeHandleProvider"
    )
  }

  return context
}

const panelContext = React.createContext<{
  id?: string
  value?: number
} | null>(null)

function usePanel() {
  const context = React.useContext(panelContext)

  if (!context) {
    throw new Error("usePanel must be used within a PanelProvider")
  }

  return context
}

const panelGroupContext = React.createContext<PanelGroupContextType | null>(
  null
)

function usePanelGroup() {
  const context = React.useContext(panelGroupContext)

  if (!context) {
    throw new Error("usePanelGroup must be used within a PanelGroupProvider")
  }

  return context
}

const ResizableHandle = React.forwardRef<
  React.ElementRef<typeof ResizeHandlePrimitive>,
  ResizeHandlePrimitiveProps & {
    withHandle?: boolean
    inset?: boolean
  }
>(
  (
    {
      className,
      children,
      disabled,
      withHandle = false,
      inset = false,
      ...props
    },
    ref
  ) => (
    <ResizeHandlePrimitive
      ref={ref}
      className={cn(
        "relative flex w-px items-center justify-center bg-border after:absolute after:inset-y-0 after:left-1/2 after:w-1 after:-translate-x-1/2 focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring focus-visible:ring-offset-1 data-[panel-group-direction=vertical]:h-px data-[panel-group-direction=vertical]:w-full data-[panel-group-direction=vertical]:after:left-0 data-[panel-group-direction=vertical]:after:h-1 data-[panel-group-direction=vertical]:after:w-full data-[panel-group-direction=vertical]:after:-translate-y-1/2 data-[panel-group-direction=vertical]:after:translate-x-0 [&[data-panel-group-direction=vertical]>div]:rotate-90",
        disabled && "cursor-not-allowed opacity-75",
        inset && "bg-transparent",
        className
      )}
      {...props}
    >
      {withHandle && (
        <div
          className={cn(
            "z-10 flex h-4 w-3 items-center justify-center rounded-sm border bg-border",
            disabled && "hidden"
          )}
        >
          {children ?? <GripVertical className="h-2.5 w-2.5" />}
        </div>
      )}
    </ResizeHandlePrimitive>
  )
)
ResizableHandle.displayName = "ResizableHandle"

// Export the context directly if needed elsewhere
export {
  PanelGroupContext,
  panelResizeHandleContext,
  panelContext,
  panelGroupContext,
  usePanelResizeHandle,
  usePanel,
  usePanelGroup,
  ResizablePanelGroup,
  ResizablePanel,
  ResizableHandle,
}
