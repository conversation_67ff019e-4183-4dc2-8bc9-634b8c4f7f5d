# **App Name**: OllamaCode

## Core Features:

- Layout: Three-column layout: file explorer, code editor, prompt area.
- File Management: File management: Create, open, save, and delete code files.
- Code Editor: Code editor with syntax highlighting and autocompletion.
- AI Integration: Prompt and interact with local Ollama models from within the client. Provides tools for using the model as an assistant.
- Plugin Support: Basic plugin support via a MCP button, allowing users to add extra features. Includes install and uninstall functionality.

## Style Guidelines:

- Primary color: Slate blue (#778DA9), reflecting technical focus and calmness.
- Background color: Dark gray (#1B262C), creating a focused coding environment.
- Accent color: Electric blue (#64CCC5) to highlight interactive elements.
- Monospace font for code readability and consistency.
- Clear separation of file explorer, editor, and prompt areas for easy navigation.
- Use a native MacOS look and feel.
- Subtle animations for file loading and Ollama response display to provide feedback.
- Use minimalist icons in the file explorer to represent different file types.