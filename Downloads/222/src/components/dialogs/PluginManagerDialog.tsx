"use client";

import React, { useState } from 'react';
import { useAppContext } from '@/contexts/AppContext';
import { Button } from '@/components/ui/button';
import { DialogContent, DialogHeader, DialogTitle, DialogFooter, DialogClose } from '@/components/ui/dialog';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { CheckCircle, XCircle, Puzzle, Search } from 'lucide-react';

export function PluginManagerDialog() {
  const { plugins, installedPlugins, installPlugin, uninstallPlugin } = useAppContext();
  const [searchTerm, setSearchTerm] = useState('');

  const filteredPlugins = plugins.filter(plugin =>
    plugin.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    plugin.description.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <DialogContent className="sm:max-w-[625px]">
      <DialogHeader>
        <DialogTitle className="flex items-center"><Puzzle size={20} className="mr-2 text-primary" />插件管理器</DialogTitle>
        <div className="relative mt-2 mb-4">
          <Search className="absolute left-2.5 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
          <Input
            type="search"
            placeholder="搜索 GitHub 上的插件..."
            className="w-full rounded-lg bg-background pl-8 h-9"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
      </DialogHeader>
      <ScrollArea className="h-[350px] pr-4">
        <div className="grid gap-4 py-1">
          {filteredPlugins.map(plugin => {
            const isInstalled = installedPlugins.includes(plugin.id);
            return (
              <Card key={plugin.id} className="bg-card">
                <CardHeader>
                  <CardTitle className="text-base">{plugin.name}</CardTitle>
                  <CardDescription className="text-xs">{plugin.description}</CardDescription>
                </CardHeader>
                <CardContent className="text-xs">
                  <p>版本： {plugin.version}</p>
                  <p>作者： {plugin.author}</p>
                </CardContent>
                <CardFooter>
                  {isInstalled ? (
                    <Button variant="destructive" size="sm" onClick={() => uninstallPlugin(plugin.id)}>
                      <XCircle size={16} className="mr-2" /> 卸载
                    </Button>
                  ) : (
                    <Button variant="default" size="sm" onClick={() => installPlugin(plugin.id)}>
                      <CheckCircle size={16} className="mr-2" /> 安装
                    </Button>
                  )}
                </CardFooter>
              </Card>
            );
          })}
          {filteredPlugins.length === 0 && (
            <p className="text-center text-muted-foreground py-4">
              {searchTerm ? `未找到与 "${searchTerm}" 相关的插件。` : "当前没有可用的插件。"}
            </p>
          )}
        </div>
      </ScrollArea>
      <DialogFooter>
        <DialogClose asChild>
          <Button type="button" variant="outline">关闭</Button>
        </DialogClose>
      </DialogFooter>
    </DialogContent>
  );
}
