
export interface CodeFile {
  id: string;
  name: string;
  content: string;
  parentId: string | null; // For folder structure
  type: 'file' | 'folder';
  language?: string; // e.g., 'javascript', 'python'
}

export interface Plugin {
  id: string;
  name: string;
  description: string;
  version: string;
  author: string;
}

export type AiActionType = 'generateFile' | 'explainCode' | 'improveCode';

// Ollama API types
export interface OllamaStreamChunk {
  model: string;
  created_at: string;
  response?: string;
  done: boolean;
  error?: string;
  context?: number[];
  total_duration?: number;
  load_duration?: number;
  prompt_eval_count?: number;
  prompt_eval_duration?: number;
  eval_count?: number;
  eval_duration?: number;
}

// Specific output types for Genkit flows will be imported from their respective flow files.
// e.g., import type { GenerateFileOutput } from '@/ai/flows/generate-file-flow';
// e.g., import type { ExplainCodeOutput } from '@/ai/flows/explain-code-flow';
// e.g., import type { ImproveCodeOutput } from '@/ai/flows/improve-code-flow';
