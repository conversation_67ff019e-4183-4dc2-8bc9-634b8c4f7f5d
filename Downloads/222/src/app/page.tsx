
"use client";

import { AppHeader } from '@/components/AppHeader';
import { FileExplorerPanel } from '@/components/FileExplorerPanel';
import { EditorPanel } from '@/components/EditorPanel';
import { PromptPanel } from '@/components/PromptPanel';
import { ResizableHandle, ResizablePanel, ResizablePanelGroup } from "@/components/ui/resizable";
import { useEffect, useState } from 'react';
import { useAppContext } from '@/contexts/AppContext';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Loader2, FolderOpen, MemoryStick, Sparkles, Code2, Github, GitFork } from 'lucide-react';

type UIState = 'initialLoading' | 'projectSelection' | 'editor';

export default function OllamaCodePage() {
  const { 
    isInitialLoadDone, 
    setIsInitialLoadDone, // Get setter
    loadFilesFromLocalStorage, 
    promptAndLoadFromDirectory, 
    startNewInMemoryProject,
    isFileSystemAccessSupported,
    isLoadingFromDirectory
  } = useAppContext();
  
  const [uiState, setUiState] = useState<UIState>('initialLoading');

  useEffect(() => {
    // This effect now correctly transitions to 'editor' if isInitialLoadDone becomes true,
    // or stays/goes to 'projectSelection' if it's false.
    const timer = setTimeout(() => {
      if (isInitialLoadDone) {
        setUiState('editor');
      } else {
        setUiState('projectSelection');
      }
    }, 50); // Reduced delay slightly

    return () => clearTimeout(timer);
  }, [isInitialLoadDone]);


  if (uiState === 'initialLoading') {
    return (
      <div className="flex flex-col h-screen bg-background text-foreground items-center justify-center">
        <Loader2 className="h-12 w-12 animate-spin text-primary" />
        <p className="mt-4 text-lg">正在初始化本地 AI 开发客户端...</p>
      </div>
    );
  }

  if (uiState === 'projectSelection') {
    return (
      <div className="flex flex-col h-screen bg-background text-foreground items-center justify-center p-4">
        <Card className="w-full max-w-lg shadow-2xl">
          <CardHeader className="text-center">
            <div className="flex items-center justify-center mb-4">
                <Code2 size={40} className="text-primary" />
            </div>
            <CardTitle className="text-2xl font-semibold">欢迎使用您的本地 AI 开发客户端</CardTitle>
            <CardDescription className="text-base">
              选择一个项目开始，或创建一个新项目。AI 助手将帮助您编写、解释和改进代码。
            </CardDescription>
          </CardHeader>
          <CardContent className="grid gap-3">
            {isFileSystemAccessSupported && (
              <Button 
                onClick={async () => {
                  const success = await promptAndLoadFromDirectory();
                  if (success) { // success means not aborted by user
                    setIsInitialLoadDone(true); // Trigger transition to editor
                  }
                  // If not success (aborted), do nothing, stay on projectSelection
                }}
                disabled={isLoadingFromDirectory}
                className="w-full justify-start py-6 text-base"
                size="lg"
              >
                {isLoadingFromDirectory ? 
                  <Loader2 className="mr-2 h-5 w-5 animate-spin" /> : 
                  <FolderOpen className="mr-2 h-5 w-5" />
                }
                导入本地文件夹
              </Button>
            )}
            <Button 
              onClick={() => {
                loadFilesFromLocalStorage(); // This already sets isInitialLoadDone
              }}
              className="w-full justify-start py-6 text-base"
              variant="secondary"
              size="lg"
            >
              <MemoryStick className="mr-2 h-5 w-5" />
              从浏览器缓存加载
            </Button>
             <Button 
              onClick={() => {}} // Placeholder for future functionality
              className="w-full justify-start py-6 text-base"
              variant="secondary"
              size="lg"
              disabled 
            >
              <Github className="mr-2 h-5 w-5" />
              从 GitHub 克隆仓库 (即将推出)
            </Button>
            <Button 
              onClick={() => {}} // Placeholder for future functionality
              className="w-full justify-start py-6 text-base"
              variant="secondary"
              size="lg"
              disabled
            >
              <GitFork className="mr-2 h-5 w-5" />
              从 Git URL 克隆 (即将推出)
            </Button>
            <Button 
              onClick={() => {
                startNewInMemoryProject(); // This already sets isInitialLoadDone
              }}
              className="w-full justify-start py-6 text-base"
              variant="outline"
              size="lg"
            >
               <Sparkles className="mr-2 h-5 w-5" />
              开始新项目 (内存存储)
            </Button>
          </CardContent>
          {!isFileSystemAccessSupported && (
            <CardFooter className="flex justify-center">
              <p className="text-xs text-muted-foreground text-center px-2 py-1 border border-dashed border-border rounded-md">
                  您的浏览器不支持直接访问本地文件夹。请考虑使用 Chrome, Edge 或其他支持文件系统访问 API 的浏览器以启用此功能。
              </p>
            </CardFooter>
          )}
        </Card>
      </div>
    );
  }

  // uiState === 'editor'
  return (
    <div className="flex flex-col h-screen bg-background text-foreground overflow-hidden">
      <AppHeader />
      <ResizablePanelGroup direction="horizontal" className="flex-grow min-h-0">
        <ResizablePanel defaultSize={20} minSize={15} maxSize={30}>
          <FileExplorerPanel />
        </ResizablePanel>
        <ResizableHandle withHandle />
        <ResizablePanel defaultSize={50} minSize={30}>
          <EditorPanel />
        </ResizablePanel>
        <ResizableHandle withHandle />
        <ResizablePanel defaultSize={30} minSize={20} maxSize={40}>
          <PromptPanel />
        </ResizablePanel>
      </ResizablePanelGroup>
    </div>
  );
}

