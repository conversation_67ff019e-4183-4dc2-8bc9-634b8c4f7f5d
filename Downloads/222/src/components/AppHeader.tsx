"use client";

import React from 'react';
import { Code2 } from 'lucide-react';

export function AppHeader() {
  return (
    <header className="flex items-center justify-between px-3 py-2 bg-[hsl(var(--sidebar-background))] border-b border-[hsl(var(--sidebar-border))] sticky top-0 z-50 h-12">
      <div className="flex items-center space-x-2">
        <div className="flex space-x-1.5">
          <span className="block w-3 h-3 bg-red-500 rounded-full opacity-70"></span>
          <span className="block w-3 h-3 bg-yellow-400 rounded-full opacity-70"></span>
          <span className="block w-3 h-3 bg-green-500 rounded-full opacity-70"></span>
        </div>
      </div>
      <div className="flex items-center text-sm font-medium text-[hsl(var(--sidebar-foreground))]">
        <Code2 size={18} className="mr-2 text-[hsl(var(--sidebar-primary))]" />
        OllamaCode
      </div>
      <div className="w-16"> {/* Placeholder for potential right-side controls */} </div>
    </header>
  );
}
