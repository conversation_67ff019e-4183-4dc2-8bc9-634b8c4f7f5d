#!/bin/bash

# OllamaCode 启动脚本
echo "🚀 启动 OllamaCode..."

# 检查 Ollama 是否运行
echo "📡 检查 Ollama 服务..."
if curl -s http://localhost:11434/api/tags > /dev/null 2>&1; then
    echo "✅ Ollama 服务正在运行"
    echo "📋 可用模型:"
    curl -s http://localhost:11434/api/tags | jq -r '.models[].name' | head -5
else
    echo "❌ Ollama 服务未运行"
    echo "请先启动 Ollama 服务: ollama serve"
    exit 1
fi

echo ""
echo "🖥️  启动桌面应用..."
echo "选择启动方式:"
echo "1) 开发模式 (推荐)"
echo "2) 仅网页版"
echo "3) 构建并运行桌面应用"

read -p "请选择 (1-3): " choice

case $choice in
    1)
        echo "🔧 启动开发模式..."
        npm run electron:dev
        ;;
    2)
        echo "🌐 启动网页版..."
        npm run dev
        ;;
    3)
        echo "📦 构建应用..."
        npm run build
        echo "🖥️  启动桌面应用..."
        npm run electron
        ;;
    *)
        echo "❌ 无效选择"
        exit 1
        ;;
esac
