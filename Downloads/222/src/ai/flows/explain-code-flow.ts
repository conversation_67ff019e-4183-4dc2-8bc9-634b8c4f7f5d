
'use server';
/**
 * @fileOverview A Genkit flow to explain selected code.
 *
 * - explainCode - A function that handles code explanation.
 * - ExplainCodeInput - The input type for the explainCode function.
 * - ExplainCodeOutput - The return type for the explainCode function.
 */

import {ai} from '@/ai/genkit';
import {z} from 'genkit';

const ExplainCodeInputSchema = z.object({
  code: z.string().describe('The code to be explained.'),
  prompt: z.string().optional().describe('An optional prompt to guide the explanation (e.g., "explain this like I am 5", or "focus on the performance aspects").'),
  fileName: z.string().optional().describe('The name of the file containing the code, for context.'),
});
export type ExplainCodeInput = z.infer<typeof ExplainCodeInputSchema>;

const ExplainCodeOutputSchema = z.object({
  explanation: z.string().describe('The explanation of the code, formatted in Markdown.'),
});
export type ExplainCodeOutput = z.infer<typeof ExplainCodeOutputSchema>;

export async function explainCode(input: ExplainCodeInput): Promise<ExplainCodeOutput> {
  return explainCodeFlow(input);
}

const promptTemplate = ai.definePrompt({
  name: 'explainCodePrompt',
  input: {schema: ExplainCodeInputSchema},
  output: {schema: ExplainCodeOutputSchema},
  prompt: `You are an expert code assistant. Your task is to explain the provided code.
{{#if fileName}}
The code is from the file: "{{fileName}}".
{{/if}}

The code to explain is:
\`\`\`
{{{code}}}
\`\`\`

{{#if prompt}}
User's specific request for the explanation: "{{prompt}}"
{{else}}
Provide a clear and concise explanation of what the code does, its purpose, and how it works. If relevant, mention any key algorithms, data structures, or notable patterns. Format your response in Markdown.
{{/if}}

Respond with the explanation in Markdown format.
`,
});

const explainCodeFlow = ai.defineFlow(
  {
    name: 'explainCodeFlow',
    inputSchema: ExplainCodeInputSchema,
    outputSchema: ExplainCodeOutputSchema,
  },
  async (input) => {
    const {output} = await promptTemplate(input);
    if (!output) {
      throw new Error("AI failed to generate an explanation for the code.");
    }
    return output;
  }
);
