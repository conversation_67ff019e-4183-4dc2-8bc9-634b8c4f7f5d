
"use client";

import React, { useEffect, useRef } from 'react';
import { useAppContext } from '@/contexts/AppContext';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { XIcon, FileText } from 'lucide-react'; // Using XIcon for close
import { ScrollArea, ScrollBar } from '@/components/ui/scroll-area';

export function EditorPanel() {
  const { 
    files, // Need full files array to get names for tabs
    openFileIds, 
    activeFileId, 
    editorContent, 
    setEditorContent, 
    setSelectedText, 
    saveActiveFile, 
    selectFile, 
    closeFileTab 
  } = useAppContext();
  
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  const activeFile = files.find(f => f.id === activeFileId);

  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if ((event.metaKey || event.ctrlKey) && event.key === 's') {
        event.preventDefault();
        if (activeFileId) {
          saveActiveFile();
        }
      }
    };
    window.addEventListener('keydown', handleKeyDown);
    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, [activeFileId, saveActiveFile]);

  const handleSelectionChange = () => {
    if (textareaRef.current) {
      const { selectionStart, selectionEnd } = textareaRef.current;
      const selected = textareaRef.current.value.substring(selectionStart, selectionEnd);
      setSelectedText(selected);
    }
  };

  return (
    <div className="h-full flex flex-col bg-background relative">
      {openFileIds.length > 0 && (
        <ScrollArea className="w-full whitespace-nowrap border-b border-border bg-[hsl(var(--card))]">
          <div className="flex space-x-0.5 px-1 py-0.5">
            {openFileIds.map((fileId) => {
              const file = files.find(f => f.id === fileId);
              if (!file) return null;
              const isActive = fileId === activeFileId;
              return (
                <div
                  key={fileId}
                  className={`flex items-center justify-between pl-3 pr-1.5 py-1.5 rounded-t-md text-xs cursor-pointer
                    ${isActive ? 'bg-background text-foreground border-x border-t border-border' : 'bg-muted text-muted-foreground hover:bg-muted/80'}`}
                  onClick={() => selectFile(fileId)}
                  title={file.name}
                >
                  <FileText size={12} className={`mr-1.5 ${isActive ? 'text-primary' : ''}`} />
                  <span className="truncate max-w-[120px] sm:max-w-[150px] md:max-w-[200px]">{file.name}</span>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-5 w-5 ml-1.5 shrink-0"
                    onClick={(e) => {
                      e.stopPropagation(); // Prevent tab selection when closing
                      closeFileTab(fileId);
                    }}
                  >
                    <XIcon size={12} />
                  </Button>
                </div>
              );
            })}
          </div>
          <ScrollBar orientation="horizontal" />
        </ScrollArea>
      )}
      
      {activeFileId && (
        <div className="p-2 border-b border-border flex justify-between items-center bg-transparent -mt-px">
          {/* This div is now mostly for potential future use, tab bar serves as header */}
          {/* <span className="text-sm font-medium text-foreground">{activeFile?.name}</span> */}
          {/* {activeFile?.language && <Badge variant="secondary">{activeFile.language}</Badge>} */}
        </div>
      )}

      <Textarea
        ref={textareaRef}
        value={editorContent}
        onChange={(e) => setEditorContent(e.target.value)}
        onSelect={handleSelectionChange}
        placeholder={activeFileId ? "开始编码..." : "打开或创建一个文件以开始编辑。"}
        className="flex-grow w-full h-full p-4 text-sm rounded-none border-none focus:ring-0 focus-visible:ring-0 resize-none bg-background text-foreground font-mono"
        style={{ fontFamily: 'var(--font-geist-mono)' }}
        disabled={!activeFileId}
      />
      {!activeFileId && (
         <div className="absolute inset-0 flex items-center justify-center bg-background opacity-80">
          <p className="text-muted-foreground">未选择文件</p>
        </div>
      )}
    </div>
  );
}
