
'use server';
/**
 * @fileOverview A Genkit flow to generate file content and a filename based on a user prompt.
 *
 * - generateFile - A function that handles the file generation process.
 * - GenerateFileInput - The input type for the generateFile function.
 * - GenerateFileOutput - The return type for the generateFile function.
 */

import {ai} from '@/ai/genkit';
import {z} from 'genkit';

const GenerateFileInputSchema = z.object({
  prompt: z.string().describe('The user prompt describing the desired file content and purpose.'),
  currentFileName: z.string().optional().describe('An optional filename suggested by the user or from existing context (e.g., when modifying a file via @filename).'),
  currentFileContent: z.string().optional().describe('Optional existing code content to be used as context or a base for the new file (e.g., when modifying a file via @filename).'),
});
export type GenerateFileInput = z.infer<typeof GenerateFileInputSchema>;

const GenerateFileOutputSchema = z.object({
  fileName: z.string().describe('The suggested filename with an appropriate extension (e.g., component.tsx, styles.css).'),
  fileContent: z.string().describe('The generated content for the file.'),
  explanation: z.string().optional().describe('A brief explanation or message from the AI regarding the generation, what was done, or what the user should be aware of. This should be concise.')
});
export type GenerateFileOutput = z.infer<typeof GenerateFileOutputSchema>;

// Define an example tool for project context
const getProjectContextTool = ai.defineTool(
  {
    name: 'getProjectContextTool',
    description: 'Returns basic context about the current project, like the main technologies used.',
    inputSchema: z.object({}), // No input needed for this example
    outputSchema: z.string(),
  },
  async () => {
    // In a real scenario, this could dynamically fetch project details.
    // For now, it's a mock response.
    return "This project is built with Next.js, React, TypeScript, ShadCN UI, and Tailwind CSS. Genkit is used for AI features.";
  }
);

export async function generateFile(input: GenerateFileInput): Promise<GenerateFileOutput> {
  return generateFileFlow(input);
}

const promptTemplate = ai.definePrompt({
  name: 'generateFilePrompt',
  input: {schema: GenerateFileInputSchema},
  output: {schema: GenerateFileOutputSchema},
  tools: [getProjectContextTool], // Make the tool available to the prompt
  prompt: `You are an expert code generation assistant.
Your task is to generate the content for a new file or modify an existing one, and suggest an appropriate filename with an extension (e.g., \`MyComponent.tsx\`, \`styles.css\`, \`utils.js\`, \`README.md\`).
You MUST also provide a brief 'explanation' of what you did or what the user should be aware of (e.g., "Generated a new React component for a login form." or "Updated the debounce function to handle async operations and added error checking.").

User's request: "{{prompt}}"

To better understand the project's requirements and generate more relevant code, you can use the 'getProjectContextTool'. This tool provides information about the project's technology stack. For example, if the project context indicates Next.js, ensure generated components are suitable for a Next.js/React environment.

{{#if currentFileName}}
You are modifying the file named: "{{currentFileName}}".
Your primary goal is to return the *complete, updated content* for this file based on the user's request and the project context. Do not just return a diff or a snippet. Output the entire new file content for "{{currentFileName}}".
The filename in your output MUST be "{{currentFileName}}".
The explanation should describe the changes made to "{{currentFileName}}".
  {{#if currentFileContent}}
  The current content of "{{currentFileName}}" is:
  \`\`\`
  {{{currentFileContent}}}
  \`\`\`
  Use this as the base for your changes.
  {{/if}}
{{else}}
The user wants to generate a new file.
Suggest a filename that is descriptive and includes an appropriate file extension based on the content, the user's prompt, and the project context (obtained via 'getProjectContextTool' if helpful).
The explanation should describe the new file's purpose and what was generated.
{{/if}}

Generate the file's content, the filename, and a brief explanation.
Return ONLY the filename, file content, and explanation in the specified JSON format.
Ensure the filename includes a relevant extension.
For example, if generating a React component in a Next.js project, the filename should be something like 'MyNewComponent.tsx'.
If modifying 'utils.js', the filename in output must be 'utils.js'.
The explanation should be concise and informative.
`,
});

const generateFileFlow = ai.defineFlow(
  {
    name: 'generateFileFlow',
    inputSchema: GenerateFileInputSchema,
    outputSchema: GenerateFileOutputSchema,
  },
  async (input) => {
    const {output} = await promptTemplate(input);
    if (!output) {
      throw new Error("Failed to generate file content from AI.");
    }

    // Ensure output filename matches input currentFileName if provided (modification mode)
    if (input.currentFileName && output.fileName !== input.currentFileName) {
      console.warn(`AI suggested a new filename '${output.fileName}' during modification of '${input.currentFileName}'. Overriding with original filename.`);
      output.fileName = input.currentFileName;
    }
    
    // Ensure filename has an extension if the AI forgets
    if (output.fileName && !output.fileName.includes('.')) {
        // Basic heuristic for extension based on content
        if (output.fileContent.trim().startsWith('<') || output.fileContent.toLowerCase().includes('react') || output.fileContent.toLowerCase().includes('jsx') || output.fileContent.toLowerCase().includes('tsx')) {
            output.fileName += '.tsx';
        } else if (output.fileContent.toLowerCase().includes('function') || output.fileContent.toLowerCase().includes('const') || output.fileContent.toLowerCase().includes('let') || output.fileContent.toLowerCase().includes('class') && (output.fileContent.toLowerCase().includes('javascript') || output.fileContent.toLowerCase().includes('typescript') )) {
            output.fileName += (input.currentFileName && input.currentFileName.endsWith('.ts')) ? '.ts' : '.js';
        } else if (output.fileContent.toLowerCase().includes('css') || output.fileContent.toLowerCase().includes('style')) {
            output.fileName += '.css';
        }  else if (output.fileContent.toLowerCase().includes('python') || output.fileContent.startsWith('#!/usr/bin/env python')) {
            output.fileName += '.py';
        } else if (output.fileContent.toLowerCase().includes('html')) {
            output.fileName += '.html';
        } else if (output.fileContent.startsWith('#')) { // Markdown
            output.fileName += '.md';
        } else {
            output.fileName += '.txt'; // Default extension
        }
    }
    return output;
  }
);

