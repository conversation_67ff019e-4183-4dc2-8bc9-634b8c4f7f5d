'use server';

import { getOllamaResponse } from '@/services/ollama';

export interface ImproveCodeInput {
  code: string;
  prompt?: string;
  fileName?: string;
}

export interface ImproveCodeOutput {
  improvedCode: string;
  explanation: string;
}

const SYSTEM_PROMPT = `You are an expert code improvement assistant.
Your task is to analyze code and provide improved versions with explanations.

Guidelines:
- Improve code quality, performance, readability, and maintainability
- Follow modern best practices and conventions
- Add proper error handling where needed
- Improve type safety (especially for TypeScript)
- Optimize performance where possible
- Ensure code follows the project's patterns
- Add helpful comments where appropriate
- Maintain the original functionality while improving the implementation

You MUST respond with a valid JSON object in the following format:
{
  "improvedCode": "the_improved_code_content",
  "explanation": "explanation_of_improvements_made_in_markdown"
}

Project Context: This is a Next.js project with React, TypeScript, ShadCN UI, and Tailwind CSS.`;

export async function improveCode(input: ImproveCodeInput): Promise<ImproveCodeOutput> {
  let prompt = `Please improve the following code:`;
  
  if (input.fileName) {
    prompt += `\n\nFile: ${input.fileName}`;
  }
  
  prompt += `\n\nCurrent code:\n\`\`\`\n${input.code}\n\`\`\``;
  
  if (input.prompt) {
    prompt += `\n\nSpecific improvement requests: ${input.prompt}`;
  }

  prompt += `\n\nProvide improved code and explain what improvements were made.
Return ONLY a valid JSON object with improvedCode and explanation fields.`;

  try {
    const response = await getOllamaResponse({
      prompt,
      systemPrompt: SYSTEM_PROMPT,
    });

    // Try to parse the JSON response
    let parsedResponse: ImproveCodeOutput;
    try {
      // Clean the response to extract JSON
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('No JSON found in response');
      }
      
      parsedResponse = JSON.parse(jsonMatch[0]);
    } catch (parseError) {
      console.error('Failed to parse AI response as JSON:', parseError);
      console.error('Raw response:', response);
      
      // Fallback: create a response based on the raw text
      parsedResponse = {
        improvedCode: input.code, // Keep original code if parsing fails
        explanation: `Failed to parse AI response. Raw response: ${response}`
      };
    }

    return parsedResponse;
  } catch (error) {
    console.error('Error improving code:', error);
    throw new Error(`Failed to improve code: ${(error as Error).message}`);
  }
}
